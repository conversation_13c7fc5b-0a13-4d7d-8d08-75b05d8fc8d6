/* Quantum-Secure Chat - Custom styles */

.quantum-text-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.quantum-border {
  border-image: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow))) 1;
}

/* Quantum particle effect for backgrounds */
.quantum-particles::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(2px 2px at 20% 30%, hsl(var(--primary-glow)), transparent),
    radial-gradient(2px 2px at 40% 70%, hsl(var(--primary)), transparent),
    radial-gradient(1px 1px at 90% 40%, hsl(var(--accent)), transparent);
  background-size: 200px 200px, 150px 150px, 100px 100px;
  animation: quantum-drift 20s linear infinite;
  opacity: 0.1;
}

@keyframes quantum-drift {
  0% {
    transform: translate(0, 0);
  }
  33% {
    transform: translate(-20px, -20px);
  }
  66% {
    transform: translate(20px, -10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

/* Hide scrollbars but keep functionality */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border));
}