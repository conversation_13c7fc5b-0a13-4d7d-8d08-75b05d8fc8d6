import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute, PublicRoute } from "@/components/auth/AuthGuard";
import { SignInForm } from "@/components/auth/SignInForm";
import { InviteValidation } from "@/components/auth/InviteValidation";
import Index from "./pages/Index";
import CircularInterface from "./pages/CircularInterface";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            {/* Public routes - redirect to chat if authenticated */}
            <Route path="/" element={<Navigate to="/chat" replace />} />
            <Route path="/auth/signin" element={
              <PublicRoute>
                <SignInForm />
              </PublicRoute>
            } />
            <Route path="/auth/invite" element={
              <PublicRoute>
                <InviteValidation />
              </PublicRoute>
            } />
            {/* Removed public signup route - invite-only system */}

            {/* Main interface - accessible to all users */}
            <Route path="/chat" element={<CircularInterface />} />
            <Route path="/profile" element={
              <ProtectedRoute>
                <Index />
              </ProtectedRoute>
            } />

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
