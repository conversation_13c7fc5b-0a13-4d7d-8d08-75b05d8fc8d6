import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  MessageSquare, 
  Users, 
  Shield, 
  Plus, 
  Settings,
  Lock,
  Timer
} from "lucide-react";

interface Chat {
  id: string;
  name: string;
  type: 'direct' | 'group' | 'channel';
  lastMessage: string;
  timestamp: string;
  unread: number;
  isQuantumSecured: boolean;
  hasExpiring: boolean;
}

const mockChats: Chat[] = [
  {
    id: '1',
    name: '<PERSON>',
    type: 'direct',
    lastMessage: 'The quantum keys are synchronized',
    timestamp: '2m ago',
    unread: 2,
    isQuantumSecured: true,
    hasExpiring: false
  },
  {
    id: '2',
    name: 'Secure Research Team',
    type: 'group',
    lastMessage: 'New PQC implementation ready',
    timestamp: '5m ago',
    unread: 0,
    isQuantumSecured: true,
    hasExpiring: true
  },
  {
    id: '3',
    name: 'QSC Announcements',
    type: 'channel',
    lastMessage: 'System upgrade completed',
    timestamp: '1h ago',
    unread: 1,
    isQuantumSecured: true,
    hasExpiring: false
  }
];

export function ChatSidebar() {
  return (
    <div className="w-80 bg-sidebar border-r border-sidebar-border flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-sidebar-border">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
            QSC
          </h1>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-quantum-secure border-quantum-secure/50">
              <Shield className="w-3 h-3 mr-1" />
              Quantum
            </Badge>
            <Button variant="ghost" size="icon">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        <Button variant="quantum" className="w-full">
          <Plus className="w-4 h-4 mr-2" />
          New Chat
        </Button>
      </div>

      {/* Chat List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {mockChats.map((chat) => (
            <div
              key={chat.id}
              className="p-3 rounded-lg hover:bg-sidebar-accent cursor-pointer transition-colors group"
            >
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    {chat.type === 'direct' && <MessageSquare className="w-4 h-4 text-muted-foreground" />}
                    {chat.type === 'group' && <Users className="w-4 h-4 text-muted-foreground" />}
                    {chat.type === 'channel' && <div className="w-4 h-4 bg-primary rounded text-primary-foreground flex items-center justify-center text-xs">#</div>}
                  </div>
                  <span className="font-medium text-sidebar-foreground">{chat.name}</span>
                </div>
                
                <div className="flex items-center gap-1">
                  {chat.isQuantumSecured && (
                    <Lock className="w-3 h-3 text-quantum-secure" />
                  )}
                  {chat.hasExpiring && (
                    <Timer className="w-3 h-3 text-quantum-warning" />
                  )}
                  <span className="text-xs text-muted-foreground">{chat.timestamp}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground truncate flex-1 mr-2">
                  {chat.lastMessage}
                </p>
                {chat.unread > 0 && (
                  <Badge variant="secondary" className="bg-primary text-primary-foreground">
                    {chat.unread}
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}