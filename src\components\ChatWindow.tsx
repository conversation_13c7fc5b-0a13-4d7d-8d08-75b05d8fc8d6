import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Send, 
  Paperclip, 
  Shield, 
  Timer, 
  MoreVertical,
  Lock,
  Check,
  CheckCheck
} from "lucide-react";

interface Message {
  id: string;
  content: string;
  sender: string;
  timestamp: string;
  isOwn: boolean;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  isQuantumSecured: boolean;
  expiresIn?: string;
}

const mockMessages: Message[] = [
  {
    id: '1',
    content: 'Hey! I\'ve just implemented the new CRYSTALS-Kyber key exchange. Ready to test?',
    sender: '<PERSON>',
    timestamp: '14:30',
    isOwn: false,
    status: 'read',
    isQuantumSecured: true
  },
  {
    id: '2',
    content: 'Perfect! Let me verify the quantum-resistant signatures first.',
    sender: 'You',
    timestamp: '14:32',
    isOwn: true,
    status: 'read',
    isQuantumSecured: true
  },
  {
    id: '3',
    content: 'All cryptographic proofs verified ✓ Forward secrecy is active.',
    sender: 'You',
    timestamp: '14:33',
    isOwn: true,
    status: 'delivered',
    isQuantumSecured: true
  },
  {
    id: '4',
    content: 'Excellent! This message will self-destruct in 1 hour.',
    sender: 'Alice Johnson',
    timestamp: '14:35',
    isOwn: false,
    status: 'read',
    isQuantumSecured: true,
    expiresIn: '58m'
  }
];

export function ChatWindow() {
  const [message, setMessage] = useState('');

  const handleSend = () => {
    if (message.trim()) {
      // Send message logic
      setMessage('');
    }
  };

  const getStatusIcon = (status: Message['status']) => {
    switch (status) {
      case 'sent':
        return <Check className="w-3 h-3" />;
      case 'delivered':
      case 'read':
        return <CheckCheck className="w-3 h-3" />;
      default:
        return null;
    }
  };

  return (
    <div className="flex-1 flex flex-col bg-background">
      {/* Chat Header */}
      <div className="p-4 border-b border-border bg-card">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary-glow rounded-full flex items-center justify-center">
              <span className="text-primary-foreground font-semibold">AJ</span>
            </div>
            <div>
              <h2 className="font-semibold text-card-foreground">Alice Johnson</h2>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-2 h-2 bg-quantum-secure rounded-full animate-quantum-pulse"></div>
                <span>Quantum-secured • Online</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="secure" className="text-xs">
              <Shield className="w-3 h-3 mr-1" />
              PQC Active
            </Badge>
            <Button variant="ghost" size="icon">
              <MoreVertical className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {mockMessages.map((msg) => (
            <div
              key={msg.id}
              className={`flex ${msg.isOwn ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[70%] rounded-lg p-3 ${
                  msg.isOwn
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-card text-card-foreground border border-border'
                }`}
              >
                <div className="flex items-start justify-between gap-2 mb-1">
                  <p className="text-sm">{msg.content}</p>
                  {msg.isQuantumSecured && (
                    <Lock className="w-3 h-3 text-quantum-secure flex-shrink-0 mt-1" />
                  )}
                </div>
                
                <div className="flex items-center justify-between text-xs opacity-75 mt-2">
                  <div className="flex items-center gap-2">
                    <span>{msg.timestamp}</span>
                    {msg.expiresIn && (
                      <Badge variant="outline" className="text-quantum-warning border-quantum-warning/50 px-1 py-0">
                        <Timer className="w-2 h-2 mr-1" />
                        {msg.expiresIn}
                      </Badge>
                    )}
                  </div>
                  
                  {msg.isOwn && (
                    <div className="flex items-center gap-1">
                      {getStatusIcon(msg.status)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Message Input */}
      <div className="p-4 border-t border-border bg-card">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon">
            <Paperclip className="w-4 h-4" />
          </Button>
          
          <div className="flex-1 relative">
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type a quantum-secured message..."
              className="pr-12"
              onKeyPress={(e) => e.key === 'Enter' && handleSend()}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Lock className="w-3 h-3 text-quantum-secure" />
            </div>
          </div>
          
          <Button 
            variant="quantum" 
            size="icon"
            onClick={handleSend}
            disabled={!message.trim()}
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex items-center justify-center mt-2">
          <Badge variant="outline" className="text-xs text-quantum-secure border-quantum-secure/50">
            <Shield className="w-3 h-3 mr-1" />
            End-to-end quantum encryption active
          </Badge>
        </div>
      </div>
    </div>
  );
}