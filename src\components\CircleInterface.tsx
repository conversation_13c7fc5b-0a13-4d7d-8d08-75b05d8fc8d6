import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from "@/components/ui/context-menu";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Shield, LogOut, User, HelpCircle, Send, Eye } from "lucide-react";

interface CircleInterfaceProps {
  isAuthenticated: boolean;
  onLogin: (code: string) => void;
  onLogout: () => void;
}

interface Message {
  id: string;
  content: string;
  sender: string;
  timestamp: string;
}

export function CircleInterface({ isAuthenticated, onLogin, onLogout }: CircleInterfaceProps) {
  const [circleState, setCircleState] = useState<'guest' | 'authenticated' | 'newMessage'>('guest');
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showSecretModal, setShowSecretModal] = useState(false);
  const [inviteCode, setInviteCode] = useState('');
  const [secretWord, setSecretWord] = useState('');
  const [newMessage, setNewMessage] = useState('');
  const [currentMessage, setCurrentMessage] = useState<Message | null>(null);
  const [messageQueue, setMessageQueue] = useState<Message[]>([]);
  const [isReading, setIsReading] = useState(false);

  // Mock messages for demo
  const mockMessages: Message[] = [
    {
      id: '1',
      content: 'This is a quantum-secured message that will self-destruct after reading.',
      sender: 'Alice',
      timestamp: new Date().toLocaleTimeString()
    },
    {
      id: '2', 
      content: 'Another secure message waiting to be read.',
      sender: 'Bob',
      timestamp: new Date().toLocaleTimeString()
    }
  ];

  useEffect(() => {
    if (isAuthenticated) {
      // Check for new messages
      if (mockMessages.length > 0) {
        setMessageQueue(mockMessages);
        setCircleState('newMessage');
      } else {
        setCircleState('authenticated');
      }
    } else {
      setCircleState('guest');
    }
  }, [isAuthenticated]);

  const handleCircleClick = () => {
    if (circleState === 'guest') {
      setShowLoginModal(true);
    } else if (circleState === 'authenticated') {
      setShowMessageModal(true);
    } else if (circleState === 'newMessage') {
      setShowSecretModal(true);
    }
  };

  const handleLogin = () => {
    if (inviteCode.trim()) {
      onLogin(inviteCode);
      setShowLoginModal(false);
      setInviteCode('');
    }
  };

  const handleSecretVerification = () => {
    if (secretWord.trim()) {
      // Show first message in queue
      const firstMessage = messageQueue[0];
      setCurrentMessage(firstMessage);
      setIsReading(true);
      setShowSecretModal(false);
      setSecretWord('');
      
      // Remove message from queue
      const remainingMessages = messageQueue.slice(1);
      setMessageQueue(remainingMessages);
      
      // Update circle state
      if (remainingMessages.length > 0) {
        setCircleState('newMessage');
      } else {
        setCircleState('authenticated');
      }
    }
  };

  const handleMessageRead = () => {
    setCurrentMessage(null);
    setIsReading(false);
  };

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      // In real app, this would send the message
      console.log('Sending message:', newMessage);
      setNewMessage('');
      setShowMessageModal(false);
    }
  };

  const getCircleColor = () => {
    switch (circleState) {
      case 'guest':
        return 'bg-red-500';
      case 'authenticated':
        return 'bg-blue-500';
      case 'newMessage':
        return 'bg-green-500 animate-pulse';
      default:
        return 'bg-red-500';
    }
  };

  const getCircleSize = () => {
    return circleState === 'newMessage' ? 'w-32 h-32' : 'w-24 h-24';
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center relative">
      {/* Background particles effect */}
      <div className="absolute inset-0 quantum-particles opacity-30"></div>
      
      {/* Main Circle Interface */}
      <ContextMenu>
        <ContextMenuTrigger>
          <div 
            className={`${getCircleColor()} ${getCircleSize()} rounded-full cursor-pointer transition-all duration-300 hover:scale-110 shadow-2xl relative z-10 flex items-center justify-center`}
            onClick={handleCircleClick}
          >
            {circleState === 'newMessage' && (
              <div className="absolute -top-2 -right-2">
                <Badge variant="destructive" className="rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                  {messageQueue.length}
                </Badge>
              </div>
            )}
            
            {circleState === 'guest' && (
              <Shield className="w-8 h-8 text-white" />
            )}
            
            {circleState === 'authenticated' && (
              <Send className="w-8 h-8 text-white" />
            )}
            
            {circleState === 'newMessage' && (
              <Eye className="w-10 h-10 text-white animate-bounce" />
            )}
          </div>
        </ContextMenuTrigger>
        
        <ContextMenuContent>
          {isAuthenticated && (
            <>
              <ContextMenuItem onClick={() => console.log('Profile')}>
                <User className="w-4 h-4 mr-2" />
                Profile Setup
              </ContextMenuItem>
              <ContextMenuItem onClick={onLogout}>
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </ContextMenuItem>
            </>
          )}
          <ContextMenuItem onClick={() => console.log('Help')}>
            <HelpCircle className="w-4 h-4 mr-2" />
            Help
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>

      {/* Status indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <p className="text-muted-foreground text-sm text-center">
          {circleState === 'guest' && 'Tap circle to login • Long press for menu'}
          {circleState === 'authenticated' && 'Tap to send message • Long press for options'}
          {circleState === 'newMessage' && 'New quantum-secured message • Tap to read'}
        </p>
      </div>

      {/* Login Modal */}
      <Dialog open={showLoginModal} onOpenChange={setShowLoginModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Quantum-Secure Access</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Input
              value={inviteCode}
              onChange={(e) => setInviteCode(e.target.value)}
              placeholder="Enter quantum-signed invite code"
              className="text-center font-mono"
            />
            <Button 
              variant="quantum" 
              className="w-full"
              onClick={handleLogin}
              disabled={!inviteCode.trim()}
            >
              <Shield className="w-4 h-4 mr-2" />
              Verify & Access
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Secret Word Modal */}
      <Dialog open={showSecretModal} onOpenChange={setShowSecretModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Message Decryption</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground text-center">
              Enter your secret word to decrypt and read the quantum-secured message
            </p>
            <Input
              type="password"
              value={secretWord}
              onChange={(e) => setSecretWord(e.target.value)}
              placeholder="Secret decryption word"
              className="text-center"
            />
            <Button 
              variant="quantum" 
              className="w-full"
              onClick={handleSecretVerification}
              disabled={!secretWord.trim()}
            >
              <Eye className="w-4 h-4 mr-2" />
              Decrypt & Read
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Message Display Modal */}
      <Dialog open={isReading && !!currentMessage} onOpenChange={handleMessageRead}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-center">Quantum-Secured Message</DialogTitle>
          </DialogHeader>
          {currentMessage && (
            <div className="space-y-4">
              <div className="bg-card/50 border border-border/50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="secure" className="text-xs">
                    <Shield className="w-3 h-3 mr-1" />
                    From: {currentMessage.sender}
                  </Badge>
                  <span className="text-xs text-muted-foreground">{currentMessage.timestamp}</span>
                </div>
                <p className="text-sm leading-relaxed">{currentMessage.content}</p>
              </div>
              <Button 
                variant="destructive" 
                className="w-full"
                onClick={handleMessageRead}
              >
                Message Read - Self Destruct
              </Button>
              <p className="text-xs text-muted-foreground text-center">
                This message will be permanently deleted after closing
              </p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Send Message Modal */}
      <Dialog open={showMessageModal} onOpenChange={setShowMessageModal}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-center">Send Quantum-Secured Message</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type your quantum-encrypted message..."
              className="min-h-[120px] resize-none"
              maxLength={500}
            />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Text only • No emojis or attachments</span>
              <span>{newMessage.length}/500</span>
            </div>
            <Button 
              variant="quantum" 
              className="w-full"
              onClick={handleSendMessage}
              disabled={!newMessage.trim()}
            >
              <Send className="w-4 h-4 mr-2" />
              Send Encrypted Message
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}