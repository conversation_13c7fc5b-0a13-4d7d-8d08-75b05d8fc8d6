import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Key, 
  Lock, 
  Zap, 
  Users, 
  Timer,
  FileText,
  Cpu
} from "lucide-react";
import heroImage from "@/assets/quantum-hero.jpg";

interface WelcomeScreenProps {
  onJoin: () => void;
}

export function WelcomeScreen({ onJoin }: WelcomeScreenProps) {
  const [inviteCode, setInviteCode] = useState('');

  const features = [
    {
      icon: Shield,
      title: "Quantum-Resistant Encryption",
      description: "CRYSTALS-Kyber and CRYSTALS-Dilithium algorithms protect against quantum computer attacks"
    },
    {
      icon: Key,
      title: "Forward Secrecy",
      description: "Messages remain secure even if keys are compromised in the future"
    },
    {
      icon: Lock,
      title: "End-to-End Encryption",
      description: "Only you and your recipients can read your messages"
    },
    {
      icon: Timer,
      title: "Self-Destructing Messages",
      description: "Set expiration times for sensitive communications"
    },
    {
      icon: Users,
      title: "Secure Group Chats",
      description: "PQC-secured group keys for team communications"
    },
    {
      icon: FileText,
      title: "Encrypted File Sharing",
      description: "Share documents and media with quantum-grade security"
    }
  ];

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{ backgroundImage: `url(${heroImage})` }}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background/80 to-transparent" />
        
        <div className="relative z-10 container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center gap-2 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary-glow rounded-lg flex items-center justify-center animate-quantum-glow">
                <Cpu className="w-6 h-6 text-primary-foreground" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
                Quantum-Secure Chat
              </h1>
            </div>
            
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Experience the future of secure communications with post-quantum cryptography. 
              Protected against classical and quantum computer attacks.
            </p>

            <div className="flex items-center justify-center gap-4 mb-8">
              <Badge variant="secure" className="text-sm">
                <Shield className="w-4 h-4 mr-2" />
                NIST PQC Standardized
              </Badge>
              <Badge variant="outline" className="text-sm border-primary-glow text-primary-glow">
                <Zap className="w-4 h-4 mr-2" />
                Quantum-Ready
              </Badge>
            </div>

            {/* Invite Code Input */}
            <Card className="max-w-md mx-auto bg-card/50 backdrop-blur-sm border-border/50">
              <CardHeader>
                <CardTitle className="text-center">Join Quantum-Secure Chat</CardTitle>
                <CardDescription className="text-center">
                  Enter your cryptographically signed invite code
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Input
                    value={inviteCode}
                    onChange={(e) => setInviteCode(e.target.value)}
                    placeholder="QSC-XXXX-XXXX-XXXX"
                    className="text-center font-mono"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Key className="w-4 h-4 text-muted-foreground" />
                  </div>
                </div>
                
                <Button 
                  variant="quantum" 
                  className="w-full"
                  onClick={onJoin}
                  disabled={!inviteCode.trim()}
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Verify & Join
                </Button>
                
                <p className="text-xs text-muted-foreground text-center">
                  Your invite will be verified using CRYSTALS-Dilithium signatures
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">
            Quantum-Grade Security Features
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="bg-card/50 border-border/50 hover:bg-card/70 transition-colors">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                      <feature.icon className="w-5 h-5 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription>{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}