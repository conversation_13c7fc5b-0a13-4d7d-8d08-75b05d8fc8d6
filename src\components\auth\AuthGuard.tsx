import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStatus } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

/**
 * AuthGuard component that protects routes based on authentication status
 * 
 * @param children - The components to render if access is allowed
 * @param requireAuth - Whether authentication is required (default: true)
 * @param redirectTo - Where to redirect if access is denied
 */
export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo 
}: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuthStatus();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    const redirect = redirectTo || '/auth/signin';
    return <Navigate to={redirect} state={{ from: location }} replace />;
  }

  // If authentication is not required but user is authenticated
  if (!requireAuth && isAuthenticated) {
    const redirect = redirectTo || '/chat';
    return <Navigate to={redirect} replace />;
  }

  // Access is allowed, render children
  return <>{children}</>;
}

/**
 * ProtectedRoute - Shorthand for routes that require authentication
 */
export function ProtectedRoute({ children }: { children: ReactNode }) {
  return (
    <AuthGuard requireAuth={true} redirectTo="/auth/signin">
      {children}
    </AuthGuard>
  );
}

/**
 * PublicRoute - Shorthand for routes that should redirect authenticated users
 */
export function PublicRoute({ children }: { children: ReactNode }) {
  return (
    <AuthGuard requireAuth={false} redirectTo="/chat">
      {children}
    </AuthGuard>
  );
}
