import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthActions } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff, Shield, CheckCircle, XCircle, UserPlus } from 'lucide-react';
import { inviteService } from '@/services/invite.service';

export function InviteValidation() {
  const [inviteCode, setInviteCode] = useState('');
  const [username, setUsername] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [secretWord, setSecretWord] = useState('');
  const [confirmSecretWord, setConfirmSecretWord] = useState('');
  const [showSecretWord, setShowSecretWord] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [inviteValid, setInviteValid] = useState(false);
  const [inviteDetails, setInviteDetails] = useState<any>(null);
  const [step, setStep] = useState<'validate' | 'register'>('validate');

  const { signUp } = useAuthActions();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination from location state
  const from = location.state?.from?.pathname || '/chat';

  // Validate invite code when it changes
  useEffect(() => {
    // QSC-XXXXXXXX format: 4 (QSC-) + 8 (suffix) = 12 characters minimum
    if (inviteCode.trim().length >= 12 && inviteCode.trim().startsWith('QSC-')) {
      validateInviteCode();
    } else {
      setInviteValid(false);
      setInviteDetails(null);
      setStep('validate');
    }
  }, [inviteCode]);

  const validateInviteCode = async () => {
    try {
      setIsLoading(true);
      const result = await inviteService.validateInviteCode(inviteCode.trim());
      setInviteValid(result.valid);

      if (result.valid) {
        setInviteDetails(result);
        setError('');
        // Auto-advance to registration step if invite is valid
        setTimeout(() => setStep('register'), 500);
      } else {
        setError(result.message || 'Invalid invite code');
        setInviteDetails(null);
        setStep('validate');
      }
    } catch (error) {
      setInviteValid(false);
      setInviteDetails(null);
      setError('Failed to validate invite code');
      setStep('validate');
    } finally {
      setIsLoading(false);
    }
  };

  const validateSecretWord = (password: string): string[] => {
    const errors: string[] = [];
    if (password.length !== 4) errors.push('Exactly 4 characters');
    if (!/[A-Z]/.test(password)) errors.push('One uppercase letter');
    if (!/[a-z]/.test(password)) errors.push('One lowercase letter');
    if (!/\d/.test(password)) errors.push('One number');
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) errors.push('One special character');
    return errors;
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username.trim() || !secretWord || !confirmSecretWord) {
      setError('Please fill in all fields');
      return;
    }

    if (secretWord !== confirmSecretWord) {
      setError('Secret words do not match');
      return;
    }

    const passwordErrors = validateSecretWord(secretWord);
    if (passwordErrors.length > 0) {
      setError(`Secret word requirements: ${passwordErrors.join(', ')}`);
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await signUp(
        username.trim(),
        secretWord,
        inviteCode.trim(),
        displayName.trim() || undefined
      );

      if (result.success) {
        navigate(from, { replace: true });
      } else {
        setError(result.error || 'Registration failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setInviteCode('');
    setUsername('');
    setDisplayName('');
    setSecretWord('');
    setConfirmSecretWord('');
    setError('');
    setInviteValid(false);
    setInviteDetails(null);
    setStep('validate');
    setShowSecretWord(false);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-900 p-4">
      <Card className="w-full max-w-md bg-slate-800 border-slate-700">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-blue-500/20 rounded-full">
              <Shield className="h-8 w-8 text-blue-400" />
            </div>
          </div>
          <CardTitle className="text-2xl text-center text-slate-100">
            {step === 'validate' ? 'Validate Invite' : 'Create Account'}
          </CardTitle>
          <CardDescription className="text-center text-slate-400">
            {step === 'validate' 
              ? 'Enter your invite code to get started'
              : 'Complete your account setup'
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive" className="bg-red-900/50 border-red-700">
              <AlertDescription className="text-red-200">{error}</AlertDescription>
            </Alert>
          )}

          {/* Step 1: Invite Code Validation */}
          {step === 'validate' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="inviteCode" className="text-slate-200">Invite Code</Label>
                <div className="relative">
                  <Input
                    id="inviteCode"
                    type="text"
                    placeholder="QSC-XXXXXXXX"
                    value={inviteCode}
                    onChange={(e) => setInviteCode(e.target.value.toUpperCase())}
                    disabled={isLoading}
                    className="bg-slate-700 border-slate-600 text-slate-100 placeholder-slate-400 text-center text-lg tracking-wider"
                    maxLength={12}
                    autoFocus
                  />
                  {inviteCode.length >= 12 && inviteCode.startsWith('QSC-') && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin text-blue-400" />
                      ) : inviteValid ? (
                        <CheckCircle className="h-4 w-4 text-green-400" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-400" />
                      )}
                    </div>
                  )}
                </div>
                <div className="text-xs text-slate-400 text-center">
                  Enter the 12-character invite code (QSC-XXXXXXXX format)
                </div>
              </div>

              {inviteValid && inviteDetails && (
                <div className="bg-green-900/20 border border-green-700/50 rounded-lg p-4">
                  <div className="flex items-center space-x-2 text-green-400 mb-2">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">Valid Invite Code</span>
                  </div>
                  <div className="text-xs text-slate-300 space-y-1">
                    <div>Expires: {new Date(inviteDetails.expiresAt).toLocaleDateString()}</div>
                    <div>Uses: {inviteDetails.currentUses}/{inviteDetails.maxUses}</div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 2: Account Registration */}
          {step === 'register' && (
            <form onSubmit={handleRegister} className="space-y-4">
              <div className="bg-blue-900/20 border border-blue-700/50 rounded-lg p-3 mb-4">
                <div className="flex items-center space-x-2 text-blue-400 text-sm">
                  <CheckCircle className="h-4 w-4" />
                  <span>Invite code validated: {inviteCode}</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="username" className="text-slate-200">Username</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="Choose a username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  disabled={isLoading}
                  className="bg-slate-700 border-slate-600 text-slate-100 placeholder-slate-400"
                  autoComplete="username"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="displayName" className="text-slate-200">Display Name (Optional)</Label>
                <Input
                  id="displayName"
                  type="text"
                  placeholder="Your display name"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  disabled={isLoading}
                  className="bg-slate-700 border-slate-600 text-slate-100 placeholder-slate-400"
                  autoComplete="name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="secretWord" className="text-slate-200">Secret Word</Label>
                <div className="relative">
                  <Input
                    id="secretWord"
                    type={showSecretWord ? 'text' : 'password'}
                    placeholder="Create a secret word"
                    value={secretWord}
                    onChange={(e) => setSecretWord(e.target.value)}
                    disabled={isLoading}
                    className="bg-slate-700 border-slate-600 text-slate-100 placeholder-slate-400"
                    autoComplete="new-password"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent text-slate-400 hover:text-slate-200"
                    onClick={() => setShowSecretWord(!showSecretWord)}
                    disabled={isLoading}
                  >
                    {showSecretWord ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                <div className="text-xs text-slate-400">
                  Must contain: exactly 4 chars (1 uppercase, 1 lowercase, 1 number, 1 special character)
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmSecretWord" className="text-slate-200">Confirm Secret Word</Label>
                <Input
                  id="confirmSecretWord"
                  type={showSecretWord ? 'text' : 'password'}
                  placeholder="Confirm your secret word"
                  value={confirmSecretWord}
                  onChange={(e) => setConfirmSecretWord(e.target.value)}
                  disabled={isLoading}
                  className="bg-slate-700 border-slate-600 text-slate-100 placeholder-slate-400"
                  autoComplete="new-password"
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Create Account
                  </>
                )}
              </Button>
            </form>
          )}
        </CardContent>
        
        <CardFooter className="flex flex-col space-y-2">
          {step === 'register' && (
            <Button
              variant="ghost"
              onClick={resetForm}
              className="text-slate-400 hover:text-slate-200"
              disabled={isLoading}
            >
              Use Different Invite Code
            </Button>
          )}
          
          <div className="text-center text-xs text-slate-400">
            <Shield className="h-4 w-4 inline mr-1" />
            Secured with post-quantum cryptography
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
