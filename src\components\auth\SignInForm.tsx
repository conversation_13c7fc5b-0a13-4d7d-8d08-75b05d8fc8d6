import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthActions } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Eye, EyeOff, Shield } from 'lucide-react';

export function SignInForm() {
  const [username, setUsername] = useState('');
  const [secretWord, setSecretWord] = useState('');
  const [showSecretWord, setShowSecretWord] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { signIn } = useAuthActions();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination from location state
  const from = location.state?.from?.pathname || '/chat';

  // Auto-submit when credentials are valid for signin
  useEffect(() => {
    if (username.trim() && secretWord.trim() && !isLoading) {
      const timeoutId = setTimeout(() => {
        handleSignIn();
      }, 500); // Small delay to avoid too frequent submissions

      return () => clearTimeout(timeoutId);
    }
  }, [username, secretWord, isLoading]);

  const handleSignIn = async () => {
    if (!username.trim() || !secretWord.trim() || isLoading) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await signIn(username.trim(), secretWord);

      if (result.success) {
        // Redirect to intended destination
        navigate(from, { replace: true });
      } else {
        setError(result.error || 'Authentication failed');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // For signin, auto-submit is handled by useEffect
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-900 p-4">
      <Card className="w-full max-w-md bg-slate-800 border-slate-700">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-red-500/20 rounded-full">
              <Shield className="h-8 w-8 text-red-400" />
            </div>
          </div>
          <CardTitle className="text-2xl text-center text-slate-100">
            Sign In
          </CardTitle>
          <CardDescription className="text-center text-slate-400">
            Enter your credentials to continue
          </CardDescription>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">

            {error && (
              <Alert variant="destructive" className="bg-red-900/50 border-red-700">
                <AlertDescription className="text-red-200">{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="username" className="text-slate-200">Username</Label>
              <Input
                id="username"
                type="text"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={isLoading}
                autoComplete="username"
                autoFocus
                className="bg-slate-700 border-slate-600 text-slate-100 placeholder-slate-400"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="secretWord" className="text-slate-200">Secret Word</Label>
              <div className="relative">
                <Input
                  id="secretWord"
                  type={showSecretWord ? 'text' : 'password'}
                  placeholder="Enter your secret word"
                  value={secretWord}
                  onChange={(e) => setSecretWord(e.target.value)}
                  disabled={isLoading}
                  autoComplete="current-password"
                  className="bg-slate-700 border-slate-600 text-slate-100 placeholder-slate-400"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent text-slate-400 hover:text-slate-200"
                  onClick={() => setShowSecretWord(!showSecretWord)}
                  disabled={isLoading}
                >
                  {showSecretWord ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showSecretWord ? 'Hide secret word' : 'Show secret word'}
                  </span>
                </Button>
              </div>
            </div>

            <div className="text-center text-sm text-slate-400">
              Auto-submitting when credentials are valid...
            </div>
          </CardContent>

          <CardFooter className="flex flex-col space-y-4">
            <div className="text-center text-sm text-slate-400">
              Credentials will be validated automatically
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
