import React from 'react';
import { useCurrentUser, useAuthActions } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { LogOut, User, Smartphone, Shield, Clock } from 'lucide-react';

export function UserProfile() {
  const { user, device, isAuthenticated } = useCurrentUser();
  const { signOut } = useAuthActions();

  if (!isAuthenticated || !user || !device) {
    return null;
  }

  const handleSignOut = async () => {
    await signOut();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-primary" />
            <CardTitle>User Profile</CardTitle>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSignOut}
            className="flex items-center space-x-2"
          >
            <LogOut className="h-4 w-4" />
            <span>Sign Out</span>
          </Button>
        </div>
        <CardDescription>
          Your Crystal Chat account information
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* User Information */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">User Details</span>
          </div>
          
          <div className="pl-6 space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Username:</span>
              <span className="font-medium">{user.username}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Display Name:</span>
              <span className="font-medium">{user.displayName}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Status:</span>
              <Badge variant={user.status === 'active' ? 'default' : 'secondary'}>
                {user.status}
              </Badge>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Last Seen:</span>
              <span className="text-xs text-muted-foreground">
                {formatDate(user.lastSeen)}
              </span>
            </div>
          </div>
        </div>

        <Separator />

        {/* Device Information */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Smartphone className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">Current Device</span>
          </div>
          
          <div className="pl-6 space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Device Name:</span>
              <span className="font-medium">{device.deviceName}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Status:</span>
              <Badge variant={device.status === 'active' ? 'default' : 'secondary'}>
                {device.status}
              </Badge>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Last Used:</span>
              <span className="text-xs text-muted-foreground">
                {formatDate(device.lastUsed)}
              </span>
            </div>
            
            <div className="flex justify-between items-start">
              <span className="text-sm text-muted-foreground">Fingerprint:</span>
              <span className="text-xs text-muted-foreground font-mono break-all max-w-32">
                {device.deviceFingerprint.substring(0, 16)}...
              </span>
            </div>
          </div>
        </div>

        <Separator />

        {/* Security Information */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">Security</span>
          </div>
          
          <div className="pl-6 space-y-2">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">Post-Quantum Encryption Enabled</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">Device Authentication Active</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">Secret Word Protection</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
