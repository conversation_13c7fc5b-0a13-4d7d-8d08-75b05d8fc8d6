import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useMessaging } from '@/hooks/useMessaging';
import { useAuth } from '@/contexts/AuthContext';
import { Send, Shield, ShieldCheck, ShieldX, Clock, Eye } from 'lucide-react';

interface ChatInterfaceProps {
  recipientId?: string;
  groupId?: string;
  title?: string;
}

export function ChatInterface({ recipientId, groupId, title }: ChatInterfaceProps) {
  const { user } = useAuth();
  const [messageInput, setMessageInput] = useState('');
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    messages,
    loading,
    error,
    sendMessage,
    markAsRead,
    participants
  } = useMessaging({
    recipientId,
    groupId,
    autoRefresh: true,
    refreshInterval: 3000
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || sending) return;

    setSending(true);
    try {
      const result = await sendMessage(messageInput);
      if (result.success) {
        setMessageInput('');
      } else {
        console.error('Failed to send message:', result.error);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleMarkAsRead = async (messageId: string) => {
    try {
      await markAsRead(messageId);
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const getStatusIcon = (status: string, verified: boolean) => {
    switch (status) {
      case 'sent':
        return <Clock className="h-3 w-3 text-gray-400" />;
      case 'delivered':
        return <Shield className="h-3 w-3 text-blue-400" />;
      case 'read':
        return <Eye className="h-3 w-3 text-green-400" />;
      case 'destroyed':
        return <ShieldX className="h-3 w-3 text-red-400" />;
      default:
        return null;
    }
  };

  const getVerificationBadge = (verified: boolean) => {
    if (verified) {
      return (
        <Badge variant="secondary" className="text-xs">
          <ShieldCheck className="h-3 w-3 mr-1" />
          Verified
        </Badge>
      );
    } else {
      return (
        <Badge variant="destructive" className="text-xs">
          <ShieldX className="h-3 w-3 mr-1" />
          Unverified
        </Badge>
      );
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span>{title || 'Secure Chat'}</span>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <Shield className="h-3 w-3 mr-1" />
              Post-Quantum Encrypted
            </Badge>
            {participants.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {participants.length} participant{participants.length > 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </CardTitle>
        {participants.length > 0 && (
          <div className="text-sm text-gray-600">
            {participants.map(p => p.displayName).join(', ')}
          </div>
        )}
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <ScrollArea className="flex-1 px-4">
          {loading && messages.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-gray-500">Loading messages...</div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-red-500">Error: {error}</div>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-gray-500">No messages yet. Start the conversation!</div>
            </div>
          ) : (
            <div className="space-y-4 py-4">
              {messages.map((message) => {
                const isOwnMessage = message.senderId === user?.id;
                
                return (
                  <div
                    key={message.id}
                    className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[70%] rounded-lg p-3 ${
                        isOwnMessage
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      {!isOwnMessage && (
                        <div className="text-xs font-medium mb-1 opacity-70">
                          {message.senderUsername}
                        </div>
                      )}
                      
                      <div className="break-words">{message.content}</div>
                      
                      <div className="flex items-center justify-between mt-2 text-xs opacity-70">
                        <div className="flex items-center space-x-2">
                          <span>{formatTimestamp(message.timestamp)}</span>
                          {getStatusIcon(message.status, message.verified)}
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          {getVerificationBadge(message.verified)}
                          {!isOwnMessage && message.status !== 'read' && message.status !== 'destroyed' && (
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 px-2 text-xs"
                              onClick={() => handleMarkAsRead(message.id)}
                            >
                              Mark as Read
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>

        {/* Message Input */}
        <div className="border-t p-4">
          <div className="flex space-x-2">
            <Input
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your secure message..."
              disabled={sending}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!messageInput.trim() || sending}
              size="sm"
            >
              {sending ? (
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          <div className="mt-2 text-xs text-gray-500 flex items-center">
            <Shield className="h-3 w-3 mr-1" />
            Messages are encrypted with CRYSTALS-Kyber and signed with CRYSTALS-Dilithium
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
