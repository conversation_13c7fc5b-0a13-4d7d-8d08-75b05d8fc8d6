import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Wifi, WifiOff, RotateCcw, Users, MessageCircle, Check, CheckCheck, Eye, Trash2 } from 'lucide-react';
import { TypingIndicator, PresenceUser, MessageDeliveryStatus } from '@/services/realtime.service';

interface RealtimeIndicatorsProps {
  connectionState: 'connected' | 'disconnected' | 'reconnecting';
  typingUsers: TypingIndicator[];
  onlineUsers: PresenceUser[];
  messageStatuses: Map<string, MessageDeliveryStatus>;
  currentUserId?: string;
  showPresence?: boolean;
  showTyping?: boolean;
  showConnectionStatus?: boolean;
}

/**
 * Real-time Messaging Indicators Component
 * Shows connection status, typing indicators, presence, and message delivery status
 */
export const RealtimeIndicators: React.FC<RealtimeIndicatorsProps> = ({
  connectionState,
  typingUsers,
  onlineUsers,
  messageStatuses,
  currentUserId,
  showPresence = true,
  showTyping = true,
  showConnectionStatus = true
}) => {
  const getConnectionIcon = () => {
    switch (connectionState) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-500" />;
      case 'disconnected':
        return <WifiOff className="w-4 h-4 text-red-500" />;
      case 'reconnecting':
        return <RotateCcw className="w-4 h-4 text-yellow-500 animate-spin" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-500" />;
    }
  };

  const getConnectionText = () => {
    switch (connectionState) {
      case 'connected':
        return 'Connected';
      case 'disconnected':
        return 'Disconnected';
      case 'reconnecting':
        return 'Reconnecting...';
      default:
        return 'Unknown';
    }
  };

  const getConnectionColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'text-green-400';
      case 'disconnected':
        return 'text-red-400';
      case 'reconnecting':
        return 'text-yellow-400';
      default:
        return 'text-slate-500';
    }
  };

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="w-3 h-3 text-slate-400" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-blue-400" />;
      case 'read':
        return <Eye className="w-3 h-3 text-green-400" />;
      case 'destroyed':
        return <Trash2 className="w-3 h-3 text-red-400" />;
      default:
        return <MessageCircle className="w-3 h-3 text-gray-300" />;
    }
  };

  // Filter typing users to exclude current user
  const activeTypingUsers = typingUsers.filter(
    user => user.isTyping && user.userId !== currentUserId
  );

  // Filter online users to exclude current user
  const otherOnlineUsers = onlineUsers.filter(
    user => user.userId !== currentUserId
  );

  return (
    <div className="space-y-2">
      {/* Connection Status */}
      {showConnectionStatus && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-xs"
        >
          {getConnectionIcon()}
          <span className={getConnectionColor()}>
            {getConnectionText()}
          </span>
          {connectionState === 'connected' && onlineUsers.length > 0 && (
            <span className="text-slate-400">
              • {onlineUsers.length} online
            </span>
          )}
        </motion.div>
      )}

      {/* Typing Indicators */}
      {showTyping && (
        <AnimatePresence>
          {activeTypingUsers.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="flex items-center gap-2 text-xs text-slate-400"
            >
              <div className="flex space-x-1">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                  className="w-1 h-1 bg-blue-400 rounded-full"
                />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                  className="w-1 h-1 bg-blue-400 rounded-full"
                />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                  className="w-1 h-1 bg-blue-400 rounded-full"
                />
              </div>
              <span>
                {activeTypingUsers.length === 1
                  ? `${activeTypingUsers[0].username} is typing...`
                  : activeTypingUsers.length === 2
                  ? `${activeTypingUsers[0].username} and ${activeTypingUsers[1].username} are typing...`
                  : `${activeTypingUsers.length} people are typing...`
                }
              </span>
            </motion.div>
          )}
        </AnimatePresence>
      )}

      {/* Online Presence */}
      {showPresence && otherOnlineUsers.length > 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center gap-2 text-xs text-slate-400"
        >
          <Users className="w-3 h-3" />
          <div className="flex items-center gap-1">
            {otherOnlineUsers.slice(0, 3).map((user, index) => (
              <div key={user.userId} className="flex items-center gap-1">
                <div
                  className={`w-2 h-2 rounded-full ${
                    user.status === 'online'
                      ? 'bg-green-400'
                      : user.status === 'away'
                      ? 'bg-yellow-400'
                      : 'bg-gray-400'
                  }`}
                />
                <span className="truncate max-w-20 text-slate-300">
                  {user.username}
                </span>
                {index < Math.min(otherOnlineUsers.length, 3) - 1 && (
                  <span className="text-slate-500">,</span>
                )}
              </div>
            ))}
            {otherOnlineUsers.length > 3 && (
              <span className="text-slate-500">
                +{otherOnlineUsers.length - 3} more
              </span>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

/**
 * Message Status Indicator Component
 * Shows delivery status for individual messages
 */
interface MessageStatusProps {
  messageId: string;
  status: string;
  timestamp?: string;
  compact?: boolean;
}

export const MessageStatus: React.FC<MessageStatusProps> = ({
  messageId,
  status,
  timestamp,
  compact = false
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'sending':
        return <RotateCcw className="w-3 h-3 text-slate-400 animate-spin" />;
      case 'sent':
        return <Check className="w-3 h-3 text-slate-400" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-blue-400" />;
      case 'read':
        return <Eye className="w-3 h-3 text-green-400" />;
      case 'destroyed':
        return <Trash2 className="w-3 h-3 text-red-400" />;
      default:
        return <MessageCircle className="w-3 h-3 text-slate-400" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'sending':
        return 'Sending...';
      case 'sent':
        return 'Sent';
      case 'delivered':
        return 'Delivered';
      case 'read':
        return 'Read';
      case 'destroyed':
        return 'Destroyed';
      default:
        return 'Unknown';
    }
  };

  const formatTimestamp = (ts?: string) => {
    if (!ts) return '';
    const date = new Date(ts);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex items-center gap-1"
      >
        {getStatusIcon()}
        {timestamp && (
          <span className="text-xs text-gray-400">
            {formatTimestamp(timestamp)}
          </span>
        )}
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex items-center gap-2 text-xs text-slate-400"
    >
      {getStatusIcon()}
      <span>{getStatusText()}</span>
      {timestamp && (
        <span className="text-slate-500">
          {formatTimestamp(timestamp)}
        </span>
      )}
    </motion.div>
  );
};

/**
 * Connection Quality Indicator
 * Shows connection quality and latency
 */
interface ConnectionQualityProps {
  connectionState: 'connected' | 'disconnected' | 'reconnecting';
  latency?: number;
}

export const ConnectionQuality: React.FC<ConnectionQualityProps> = ({
  connectionState,
  latency
}) => {
  const getQualityBars = () => {
    if (connectionState !== 'connected') {
      return [false, false, false];
    }

    if (!latency) {
      return [true, true, true]; // Assume good if no latency data
    }

    if (latency < 100) {
      return [true, true, true]; // Excellent
    } else if (latency < 300) {
      return [true, true, false]; // Good
    } else if (latency < 500) {
      return [true, false, false]; // Fair
    } else {
      return [false, false, false]; // Poor
    }
  };

  const bars = getQualityBars();
  const isConnected = connectionState === 'connected';

  return (
    <div className="flex items-center gap-1">
      {bars.map((active, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, scaleY: 0 }}
          animate={{ 
            opacity: isConnected ? (active ? 1 : 0.3) : 0.2, 
            scaleY: 1 
          }}
          transition={{ delay: index * 0.1 }}
          className={`w-1 rounded-sm ${
            active && isConnected
              ? index === 0
                ? 'bg-green-400 h-2'
                : index === 1
                ? 'bg-green-400 h-3'
                : 'bg-green-400 h-4'
              : 'bg-slate-600 h-2'
          }`}
        />
      ))}
      {latency && isConnected && (
        <span className="text-xs text-slate-400 ml-1">
          {latency}ms
        </span>
      )}
    </div>
  );
};
