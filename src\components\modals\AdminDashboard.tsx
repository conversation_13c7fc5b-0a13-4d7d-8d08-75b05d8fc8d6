import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Shield, 
  Users, 
  Activity, 
  UserCheck, 
  UserX, 
  Clock, 
  CheckCircle, 
  XCircle,
  Settings,
  Database,
  MessageSquare,
  UserPlus
} from 'lucide-react';
import { adminService, AdminUser, PendingInvite, SystemStats } from '@/services/admin.service';
import { PendingInvitesPanel } from './admin/PendingInvitesPanel';
import { UserManagementPanel } from './admin/UserManagementPanel';
import { SystemStatusPanel } from './admin/SystemStatusPanel';

interface AdminDashboardProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AdminDashboard({ isOpen, onClose }: AdminDashboardProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [pendingInvites, setPendingInvites] = useState<PendingInvite[]>([]);

  useEffect(() => {
    if (isOpen) {
      loadDashboardData();
    }
  }, [isOpen]);

  const loadDashboardData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load system stats
      const statsResult = await adminService.getSystemStats();
      if (statsResult.success && statsResult.stats) {
        setSystemStats(statsResult.stats);
      }

      // Load users
      const usersResult = await adminService.getAllUsers();
      if (usersResult.success && usersResult.users) {
        setUsers(usersResult.users);
      }

      // Load pending invites
      const invitesResult = await adminService.getPendingInvites();
      if (invitesResult.success && invitesResult.invites) {
        setPendingInvites(invitesResult.invites);
      }
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadDashboardData();
  };

  const getOverviewCards = () => {
    if (!systemStats) return [];

    return [
      {
        title: 'Total Users',
        value: systemStats.totalUsers,
        icon: Users,
        description: `${systemStats.activeUsers} active`,
        color: 'text-blue-500'
      },
      {
        title: 'Messages Today',
        value: systemStats.messagesToday,
        icon: MessageSquare,
        description: `${systemStats.totalMessages} total`,
        color: 'text-green-500'
      },
      {
        title: 'Pending Invites',
        value: systemStats.pendingInvites,
        icon: UserPlus,
        description: `${systemStats.totalInvites} total`,
        color: 'text-orange-500'
      },
      {
        title: 'Active Devices',
        value: systemStats.activeDevices,
        icon: Database,
        description: 'Connected devices',
        color: 'text-purple-500'
      }
    ];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-6 w-6 text-blue-500" />
            <span>Admin Dashboard</span>
            <Badge variant="outline" className="ml-2">
              <Settings className="h-3 w-3 mr-1" />
              Admin Access
            </Badge>
          </DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-5 w-5 text-red-500" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="invites">
              Pending Invites
              {pendingInvites.length > 0 && (
                <Badge variant="destructive" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                  {pendingInvites.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="users">User Management</TabsTrigger>
            <TabsTrigger value="system">System Status</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">System Overview</h3>
              <Button onClick={handleRefresh} disabled={loading} variant="outline" size="sm">
                <Activity className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {getOverviewCards().map((card, index) => (
                <motion.div
                  key={card.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        {card.title}
                      </CardTitle>
                      <card.icon className={`h-4 w-4 ${card.color}`} />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{card.value}</div>
                      <p className="text-xs text-muted-foreground">
                        {card.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common administrative tasks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    onClick={() => setActiveTab('invites')} 
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-center space-y-2"
                  >
                    <UserPlus className="h-6 w-6" />
                    <span>Manage Invites</span>
                    {pendingInvites.length > 0 && (
                      <Badge variant="destructive">{pendingInvites.length} pending</Badge>
                    )}
                  </Button>
                  
                  <Button 
                    onClick={() => setActiveTab('users')} 
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-center space-y-2"
                  >
                    <Users className="h-6 w-6" />
                    <span>Manage Users</span>
                    <Badge variant="outline">{users.length} total</Badge>
                  </Button>
                  
                  <Button 
                    onClick={() => setActiveTab('system')} 
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-center space-y-2"
                  >
                    <Activity className="h-6 w-6" />
                    <span>System Status</span>
                    <Badge variant="outline" className="text-green-600">Healthy</Badge>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invites">
            <PendingInvitesPanel 
              invites={pendingInvites}
              onRefresh={handleRefresh}
              loading={loading}
            />
          </TabsContent>

          <TabsContent value="users">
            <UserManagementPanel 
              users={users}
              onRefresh={handleRefresh}
              loading={loading}
            />
          </TabsContent>

          <TabsContent value="system">
            <SystemStatusPanel 
              stats={systemStats}
              onRefresh={handleRefresh}
              loading={loading}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
