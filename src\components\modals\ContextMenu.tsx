import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { LogOut, User, HelpCircle, Shield, Settings, Key, Activity } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface ContextMenuProps {
  isOpen: boolean;
  onClose: () => void;
  onProfileClick: () => void;
  onHelpClick: () => void;
  onPerformanceClick: () => void;
}

export function ContextMenu({ isOpen, onClose, onProfileClick, onHelpClick, onPerformanceClick }: ContextMenuProps) {
  const { user, device, signOut } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
      onClose();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleProfileClick = () => {
    onProfileClick();
    onClose();
  };

  const handleHelpClick = () => {
    onHelpClick();
    onClose();
  };

  const handlePerformanceClick = () => {
    onPerformanceClick();
    onClose();
  };

  const menuItems = [
    {
      icon: User,
      label: 'Profile',
      description: 'View your profile and device info',
      onClick: handleProfileClick,
      variant: 'default' as const
    },
    {
      icon: HelpCircle,
      label: 'Help',
      description: 'Learn about Crystal Chat',
      onClick: handleHelpClick,
      variant: 'default' as const
    },
    {
      icon: Activity,
      label: 'Performance',
      description: 'Monitor crypto performance',
      onClick: handlePerformanceClick,
      variant: 'default' as const
    },
    {
      icon: LogOut,
      label: 'Sign Out',
      description: 'Sign out and clear all data',
      onClick: handleSignOut,
      variant: 'destructive' as const
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-sm p-0 gap-0">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="p-6"
        >
          {/* User Info Header */}
          <div className="mb-6">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">{user?.displayName}</h3>
                <p className="text-sm text-gray-600">@{user?.username}</p>
              </div>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-lg space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Device:</span>
                <span className="font-medium">{device?.deviceName}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Security:</span>
                <div className="flex items-center space-x-1">
                  <Shield className="h-3 w-3 text-green-500" />
                  <span className="text-green-600 font-medium">Quantum-Safe</span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Status:</span>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-600 font-medium">Active</span>
                </div>
              </div>
            </div>
          </div>

          <Separator className="mb-4" />

          {/* Menu Items */}
          <div className="space-y-2">
            {menuItems.map((item, index) => (
              <motion.div
                key={item.label}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Button
                  variant={item.variant === 'destructive' ? 'destructive' : 'ghost'}
                  className="w-full justify-start h-auto p-3"
                  onClick={item.onClick}
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="h-5 w-5" />
                    <div className="text-left">
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs opacity-70">{item.description}</div>
                    </div>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>

          {/* Footer */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="text-center text-xs text-gray-500 space-y-1">
              <div className="flex items-center justify-center space-x-1">
                <Shield className="h-3 w-3" />
                <span>Crystal Chat</span>
              </div>
              <div>Post-Quantum Secure Messaging</div>
            </div>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ProfileModal({ isOpen, onClose }: ProfileModalProps) {
  const { user, device } = useAuth();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="space-y-6"
        >
          <div className="text-center">
            <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="h-10 w-10 text-white" />
            </div>
            <h2 className="text-2xl font-bold">{user?.displayName}</h2>
            <p className="text-gray-600">@{user?.username}</p>
          </div>

          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3 flex items-center">
                <Settings className="h-4 w-4 mr-2" />
                Device Information
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Device Name:</span>
                  <span className="font-medium">{device?.deviceName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Device ID:</span>
                  <span className="font-mono text-xs">{device?.id.slice(0, 8)}...</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className="text-green-600 font-medium">Active</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Used:</span>
                  <span>{device?.lastUsed ? new Date(device.lastUsed).toLocaleDateString() : 'Now'}</span>
                </div>
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h3 className="font-semibold mb-3 flex items-center text-green-800">
                <Key className="h-4 w-4 mr-2" />
                Security Status
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-green-700">Encryption:</span>
                  <div className="flex items-center space-x-1">
                    <Shield className="h-3 w-3 text-green-600" />
                    <span className="text-green-600 font-medium">CRYSTALS-Kyber</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-green-700">Signatures:</span>
                  <div className="flex items-center space-x-1">
                    <Shield className="h-3 w-3 text-green-600" />
                    <span className="text-green-600 font-medium">CRYSTALS-Dilithium</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-green-700">Quantum Resistance:</span>
                  <span className="text-green-600 font-medium">✓ Enabled</span>
                </div>
              </div>
            </div>
          </div>

          <Button onClick={onClose} className="w-full">
            Close
          </Button>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HelpModal({ isOpen, onClose }: HelpModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg max-h-[80vh] overflow-y-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Shield className="h-12 w-12 text-blue-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold">Crystal Chat Help</h2>
            <p className="text-gray-600">Post-Quantum Secure Messaging</p>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">🔴 Red Circle (Guest)</h3>
              <p className="text-sm text-gray-600">
                Tap to sign in or create an account. Long press for help menu.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-2">🔵 Blue Circle (Authenticated)</h3>
              <p className="text-sm text-gray-600">
                Tap to send a new message. Long press for profile and settings.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-2">🟢 Green Circle (New Messages)</h3>
              <p className="text-sm text-gray-600">
                Tap to read messages. Messages are shown one at a time and self-destruct after reading.
              </p>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
              <h3 className="font-semibold mb-2 text-orange-800">⚠️ Important Security Notes</h3>
              <ul className="text-sm text-orange-700 space-y-1">
                <li>• Messages are destroyed immediately after reading</li>
                <li>• You must re-enter your secret word to read messages</li>
                <li>• All messages use post-quantum cryptography</li>
                <li>• No message history is stored anywhere</li>
                <li>• Text-only platform - no emojis or attachments</li>
              </ul>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h3 className="font-semibold mb-2 text-blue-800">🔐 Cryptography</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• CRYSTALS-Kyber for quantum-resistant encryption</li>
                <li>• CRYSTALS-Dilithium for digital signatures</li>
                <li>• AES-256-GCM for symmetric encryption</li>
                <li>• Forward secrecy for all communications</li>
              </ul>
            </div>
          </div>

          <Button onClick={onClose} className="w-full">
            Got it!
          </Button>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
