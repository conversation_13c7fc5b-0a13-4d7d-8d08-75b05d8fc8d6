import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, Shield, UserPlus, LogIn, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function LoginModal({ isOpen, onClose }: LoginModalProps) {
  const { signIn } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  // Sign In Form State
  const [signInData, setSignInData] = useState({
    username: '',
    secretWord: ''
  });



  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!signInData.username.trim() || !signInData.secretWord) {
      setError('Please fill in all fields');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await signIn(signInData.username.trim(), signInData.secretWord);
      if (result.success) {
        onClose();
        setSignInData({ username: '', secretWord: '' });
      } else {
        setError(result.error || 'Sign in failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = () => {
    onClose();
    navigate('/auth/invite');
  };

  const resetForm = () => {
    setSignInData({ username: '', secretWord: '' });
    setError(null);
    setShowPassword(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-center space-x-2">
            <Shield className="h-6 w-6 text-blue-500" />
            <span>Crystal Chat</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
                <form onSubmit={handleSignIn} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="signin-username">Username</Label>
                    <Input
                      id="signin-username"
                      type="text"
                      value={signInData.username}
                      onChange={(e) => setSignInData(prev => ({ ...prev, username: e.target.value }))}
                      placeholder="Enter your username"
                      disabled={loading}
                      autoComplete="username"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signin-secret">Secret Word</Label>
                    <div className="relative">
                      <Input
                        id="signin-secret"
                        type={showPassword ? "text" : "password"}
                        value={signInData.secretWord}
                        onChange={(e) => setSignInData(prev => ({ ...prev, secretWord: e.target.value }))}
                        placeholder="Enter your secret word"
                        disabled={loading}
                        autoComplete="current-password"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={loading}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Signing In...
                      </>
                    ) : (
                      <>
                        <LogIn className="mr-2 h-4 w-4" />
                        Sign In
                      </>
                    )}
                  </Button>
                </form>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Or
                    </span>
                  </div>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={handleCreateAccount}
                  disabled={loading}
                >
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create Account with Invite
                </Button>
              </motion.div>
            </div>


        <div className="text-center text-xs text-gray-500 mt-4">
          <Shield className="h-4 w-4 inline mr-1" />
          Secured with post-quantum cryptography
        </div>
      </DialogContent>
    </Dialog>
  );
}
