import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Send, Eye, EyeOff, Shield, ShieldCheck, ShieldX, Clock, Trash2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Message } from '@/services/messaging.service';
import { userDiscoveryService } from '@/services/userDiscovery.service';

interface MessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'send' | 'read';
  message?: Message;
  onSendMessage?: (content: string, recipientUsername: string) => Promise<{ success: boolean; error?: string }>;
  onMarkAsRead?: (messageId: string) => Promise<{ success: boolean; error?: string }>;
}

export function MessageModal({
  isOpen,
  onClose,
  mode,
  message,
  onSendMessage,
  onMarkAsRead
}: MessageModalProps) {
  const { signIn, user, isAuthenticated, refreshSession } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSecretWord, setShowSecretWord] = useState(false);
  const [authenticationRequired, setAuthenticationRequired] = useState(false);
  const [recipientValidation, setRecipientValidation] = useState<{
    isValidating: boolean;
    isValid: boolean | null;
    error: string | null;
    suggestion: string | null;
  }>({
    isValidating: false,
    isValid: null,
    error: null,
    suggestion: null
  });

  // Send mode state
  const [sendData, setSendData] = useState({
    recipientUsername: '',
    messageContent: '',
    secretWord: ''
  });

  // Read mode state
  const [readData, setReadData] = useState({
    secretWord: ''
  });

  const [messageRevealed, setMessageRevealed] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      // Reset state when modal closes
      setSendData({ recipientUsername: '', messageContent: '', secretWord: '' });
      setReadData({ secretWord: '' });
      setMessageRevealed(false);
      setError(null);
      setShowSecretWord(false);
      setAuthenticationRequired(false);
      setRecipientValidation({
        isValidating: false,
        isValid: null,
        error: null,
        suggestion: null
      });
    }
  }, [isOpen]);

  // Check authentication status when modal opens
  useEffect(() => {
    if (isOpen && !isAuthenticated) {
      setError('Please log in to continue');
      setAuthenticationRequired(true);
    } else if (isOpen && isAuthenticated) {
      setAuthenticationRequired(false);
      // Verify session is still valid
      validateSession();
    }
  }, [isOpen, isAuthenticated]);

  const validateSession = async () => {
    try {
      const sessionValid = await refreshSession();
      if (!sessionValid) {
        setError('Your session has expired. Please re-authenticate.');
        setAuthenticationRequired(true);
      }
    } catch (error) {
      console.error('Session validation error:', error);
      setError('Session validation failed. Please re-authenticate.');
      setAuthenticationRequired(true);
    }
  };

  // Debounced recipient validation
  useEffect(() => {
    if (mode !== 'send' || !sendData.recipientUsername.trim()) {
      setRecipientValidation({
        isValidating: false,
        isValid: null,
        error: null,
        suggestion: null
      });
      return;
    }

    const timeoutId = setTimeout(() => {
      validateRecipient(sendData.recipientUsername.trim());
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [sendData.recipientUsername, mode]);

  const validateRecipient = async (username: string) => {
    if (!username || username.length < 3) {
      setRecipientValidation({
        isValidating: false,
        isValid: null,
        error: null,
        suggestion: null
      });
      return;
    }

    setRecipientValidation(prev => ({
      ...prev,
      isValidating: true,
      error: null,
      suggestion: null
    }));

    try {
      // First validate username format
      const formatValidation = userDiscoveryService.validateUsernameFormat(username);
      if (!formatValidation.valid) {
        setRecipientValidation({
          isValidating: false,
          isValid: false,
          error: formatValidation.errors[0],
          suggestion: 'Please check the username format'
        });
        return;
      }

      // Then validate if recipient exists and can receive messages
      const result = await userDiscoveryService.validateRecipient(username);

      setRecipientValidation({
        isValidating: false,
        isValid: result.valid,
        error: result.valid ? null : result.error || 'Username validation failed',
        suggestion: result.valid ? null : result.suggestion
      });
    } catch (error) {
      console.error('Recipient validation error:', error);
      setRecipientValidation({
        isValidating: false,
        isValid: false,
        error: 'Unable to validate username',
        suggestion: 'Please check your connection and try again'
      });
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!sendData.recipientUsername.trim() || !sendData.messageContent.trim() || !sendData.secretWord) {
      setError('Please fill in all fields');
      return;
    }

    if (!onSendMessage) {
      setError('Send function not available');
      return;
    }

    // Check recipient validation
    if (recipientValidation.isValidating) {
      setError('Please wait while we validate the recipient username');
      return;
    }

    if (recipientValidation.isValid === false) {
      setError(recipientValidation.error || 'Invalid recipient username');
      return;
    }

    // If we haven't validated yet, validate now
    if (recipientValidation.isValid === null) {
      await validateRecipient(sendData.recipientUsername.trim());
      // Don't proceed immediately, let the user try again after validation
      return;
    }

    if (!user?.username) {
      setError('User information not available. Please log in again.');
      setAuthenticationRequired(true);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Re-authenticate with current user's credentials (not recipient's)
      const authResult = await signIn(user.username, sendData.secretWord);
      if (!authResult.success) {
        setError('Invalid secret word. Please verify your credentials.');
        setLoading(false);
        return;
      }

      // Validate session after re-authentication
      const sessionValid = await refreshSession();
      if (!sessionValid) {
        setError('Authentication failed. Please try again.');
        setLoading(false);
        return;
      }

      // Send the message
      const result = await onSendMessage(sendData.messageContent.trim(), sendData.recipientUsername.trim());

      if (result.success) {
        onClose();
      } else {
        setError(result.error || 'Failed to send message');
      }
    } catch (err) {
      console.error('Send message error:', err);
      setError('An unexpected error occurred while sending the message');
    } finally {
      setLoading(false);
    }
  };

  const handleReadMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!readData.secretWord || !message) {
      setError('Secret word is required to read messages');
      return;
    }

    if (!onMarkAsRead) {
      setError('Read function not available');
      return;
    }

    if (!user?.username) {
      setError('User information not available. Please log in again.');
      setAuthenticationRequired(true);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Re-authenticate with current user's credentials
      const authResult = await signIn(user.username, readData.secretWord);
      if (!authResult.success) {
        setError('Invalid secret word. Please verify your credentials.');
        setLoading(false);
        return;
      }

      // Validate session after re-authentication
      const sessionValid = await refreshSession();
      if (!sessionValid) {
        setError('Authentication failed. Please try again.');
        setLoading(false);
        return;
      }

      // Mark message as read (this will trigger self-destruction)
      const result = await onMarkAsRead(message.id);

      if (result.success) {
        setMessageRevealed(true);
        // Auto-close after 10 seconds
        setTimeout(() => {
          onClose();
        }, 10000);
      } else {
        setError(result.error || 'Failed to read message');
      }
    } catch (err) {
      console.error('Read message error:', err);
      setError('An unexpected error occurred while reading the message');
    } finally {
      setLoading(false);
    }
  };

  const handleReAuthenticate = async () => {
    if (!user?.username) {
      setError('User information not available. Please log in again.');
      return;
    }

    const secretWord = mode === 'send' ? sendData.secretWord : readData.secretWord;
    if (!secretWord) {
      setError('Please enter your secret word to continue');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const authResult = await signIn(user.username, secretWord);
      if (authResult.success) {
        setAuthenticationRequired(false);
        setError(null);
      } else {
        setError('Invalid secret word. Please try again.');
      }
    } catch (err) {
      console.error('Re-authentication error:', err);
      setError('Re-authentication failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const getVerificationBadge = (verified: boolean) => {
    if (verified) {
      return (
        <Badge variant="secondary" className="text-xs">
          <ShieldCheck className="h-3 w-3 mr-1" />
          Verified
        </Badge>
      );
    } else {
      return (
        <Badge variant="destructive" className="text-xs">
          <ShieldX className="h-3 w-3 mr-1" />
          Unverified
        </Badge>
      );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-500" />
              <span>{mode === 'send' ? 'Send Message' : 'Read Message'}</span>
            </div>
            <Badge variant="outline" className="text-xs">
              <Shield className="h-3 w-3 mr-1" />
              Quantum-Safe
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <AnimatePresence mode="wait">
          {mode === 'send' ? (
            <motion.div
              key="send"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <form onSubmit={handleSendMessage} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="recipient">Recipient Username</Label>
                  <div className="relative">
                    <Input
                      id="recipient"
                      type="text"
                      value={sendData.recipientUsername}
                      onChange={(e) => setSendData(prev => ({ ...prev, recipientUsername: e.target.value }))}
                      placeholder="Enter recipient's username"
                      disabled={loading}
                      className={`${
                        recipientValidation.isValid === false
                          ? 'border-red-500 focus:border-red-500'
                          : recipientValidation.isValid === true
                          ? 'border-green-500 focus:border-green-500'
                          : ''
                      }`}
                    />
                    {recipientValidation.isValidating && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full" />
                      </div>
                    )}
                    {recipientValidation.isValid === true && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <ShieldCheck className="h-4 w-4 text-green-500" />
                      </div>
                    )}
                    {recipientValidation.isValid === false && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <ShieldX className="h-4 w-4 text-red-500" />
                      </div>
                    )}
                  </div>

                  {/* Validation feedback */}
                  {recipientValidation.error && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-600 bg-red-50 p-2 rounded-md"
                    >
                      <div>{recipientValidation.error}</div>
                      {recipientValidation.suggestion && (
                        <div className="text-xs text-red-500 mt-1">
                          {recipientValidation.suggestion}
                        </div>
                      )}
                    </motion.div>
                  )}

                  {recipientValidation.isValid === true && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-green-600 bg-green-50 p-2 rounded-md"
                    >
                      ✓ Recipient verified and can receive messages
                    </motion.div>
                  )}

                  <div className="text-xs text-gray-500">
                    Enter the exact username. No suggestions will be provided to maintain privacy.
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    value={sendData.messageContent}
                    onChange={(e) => setSendData(prev => ({ ...prev, messageContent: e.target.value }))}
                    placeholder="Type your message here..."
                    disabled={loading}
                    rows={4}
                    maxLength={1000}
                  />
                  <div className="text-xs text-gray-500 text-right">
                    {sendData.messageContent.length}/1000 characters
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="secret">Your Secret Word</Label>
                  <div className="relative">
                    <Input
                      id="secret"
                      type={showSecretWord ? "text" : "password"}
                      value={sendData.secretWord}
                      onChange={(e) => setSendData(prev => ({ ...prev, secretWord: e.target.value }))}
                      placeholder="Enter your secret word to send"
                      disabled={loading}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowSecretWord(!showSecretWord)}
                      disabled={loading}
                    >
                      {showSecretWord ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <div className="text-xs text-gray-600">
                    Required for authentication and message encryption
                  </div>
                </div>

                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-sm text-red-600 bg-red-50 p-3 rounded-md"
                  >
                    {error}
                  </motion.div>
                )}

                {authenticationRequired && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-sm text-orange-600 bg-orange-50 p-3 rounded-md border border-orange-200"
                  >
                    <div className="flex items-center justify-between">
                      <span>Authentication required to continue</span>
                      <Button
                        type="button"
                        size="sm"
                        onClick={handleReAuthenticate}
                        disabled={loading || !sendData.secretWord}
                        className="ml-2"
                      >
                        Re-authenticate
                      </Button>
                    </div>
                  </motion.div>
                )}

                <div className="flex space-x-2">
                  <Button type="button" variant="outline" onClick={onClose} disabled={loading} className="flex-1">
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      loading ||
                      authenticationRequired ||
                      recipientValidation.isValidating ||
                      recipientValidation.isValid !== true
                    }
                    className="flex-1"
                  >
                    {loading ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                        <span>Sending...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Send className="h-4 w-4" />
                        <span>Send</span>
                      </div>
                    )}
                  </Button>
                </div>
              </form>

              <div className="text-xs text-gray-500 text-center bg-gray-50 p-2 rounded">
                <Trash2 className="h-3 w-3 inline mr-1" />
                Message will self-destruct when read by recipient
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="read"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {!messageRevealed ? (
                <form onSubmit={handleReadMessage} className="space-y-4">
                  {message && (
                    <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">From: {message.senderUsername}</span>
                        {getVerificationBadge(message.verified)}
                      </div>
                      <div className="text-sm text-gray-600">
                        <Clock className="h-3 w-3 inline mr-1" />
                        {formatTimestamp(message.timestamp)}
                      </div>
                      <div className="text-sm text-orange-600 bg-orange-50 p-2 rounded">
                        ⚠️ This message will be destroyed after reading
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="read-secret">Your Secret Word</Label>
                    <div className="relative">
                      <Input
                        id="read-secret"
                        type={showSecretWord ? "text" : "password"}
                        value={readData.secretWord}
                        onChange={(e) => setReadData(prev => ({ ...prev, secretWord: e.target.value }))}
                        placeholder="Enter your secret word to read"
                        disabled={loading}
                        autoFocus
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowSecretWord(!showSecretWord)}
                        disabled={loading}
                      >
                        {showSecretWord ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <div className="text-xs text-gray-600">
                      Required to decrypt and read the message
                    </div>
                  </div>

                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-600 bg-red-50 p-3 rounded-md"
                    >
                      {error}
                    </motion.div>
                  )}

                  {authenticationRequired && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-orange-600 bg-orange-50 p-3 rounded-md border border-orange-200"
                    >
                      <div className="flex items-center justify-between">
                        <span>Authentication required to continue</span>
                        <Button
                          type="button"
                          size="sm"
                          onClick={handleReAuthenticate}
                          disabled={loading || !readData.secretWord}
                          className="ml-2"
                        >
                          Re-authenticate
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  <div className="flex space-x-2">
                    <Button type="button" variant="outline" onClick={onClose} disabled={loading} className="flex-1">
                      Cancel
                    </Button>
                    <Button type="submit" disabled={loading || authenticationRequired} className="flex-1">
                      {loading ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                          <span>Reading...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <Eye className="h-4 w-4" />
                          <span>Read Message</span>
                        </div>
                      )}
                    </Button>
                  </div>
                </form>
              ) : (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="space-y-4"
                >
                  {message && (
                    <>
                      <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-green-800">From: {message.senderUsername}</span>
                          {getVerificationBadge(message.verified)}
                        </div>
                        <div className="text-sm text-green-600 mb-3">
                          <Clock className="h-3 w-3 inline mr-1" />
                          {formatTimestamp(message.timestamp)}
                        </div>
                        <div className="bg-white p-3 rounded border border-green-200">
                          <p className="text-gray-900 whitespace-pre-wrap">{message.content}</p>
                        </div>
                      </div>

                      <div className="text-center text-sm text-red-600 bg-red-50 p-3 rounded-lg">
                        <Trash2 className="h-4 w-4 inline mr-1" />
                        This message has been destroyed and will close automatically
                      </div>

                      <Button onClick={onClose} className="w-full">
                        Close
                      </Button>
                    </>
                  )}
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
