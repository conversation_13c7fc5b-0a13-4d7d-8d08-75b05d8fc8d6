import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Send, Eye, EyeOff, Shield, ShieldCheck, ShieldX, Clock, Trash2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Message } from '@/services/messaging.service';

interface MessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'send' | 'read';
  message?: Message;
  onSendMessage?: (content: string, recipientUsername: string) => Promise<{ success: boolean; error?: string }>;
  onMarkAsRead?: (messageId: string) => Promise<{ success: boolean; error?: string }>;
}

export function MessageModal({
  isOpen,
  onClose,
  mode,
  message,
  onSendMessage,
  onMarkAsRead
}: MessageModalProps) {
  const { signIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSecretWord, setShowSecretWord] = useState(false);

  // Send mode state
  const [sendData, setSendData] = useState({
    recipientUsername: '',
    messageContent: '',
    secretWord: ''
  });

  // Read mode state
  const [readData, setReadData] = useState({
    secretWord: ''
  });

  const [messageRevealed, setMessageRevealed] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      // Reset state when modal closes
      setSendData({ recipientUsername: '', messageContent: '', secretWord: '' });
      setReadData({ secretWord: '' });
      setMessageRevealed(false);
      setError(null);
      setShowSecretWord(false);
    }
  }, [isOpen]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!sendData.recipientUsername.trim() || !sendData.messageContent.trim() || !sendData.secretWord) {
      setError('Please fill in all fields');
      return;
    }

    if (!onSendMessage) {
      setError('Send function not available');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Re-authenticate with secret word
      const authResult = await signIn(sendData.recipientUsername.trim(), sendData.secretWord);
      if (!authResult.success) {
        setError('Invalid secret word');
        setLoading(false);
        return;
      }

      // Send the message
      const result = await onSendMessage(sendData.messageContent.trim(), sendData.recipientUsername.trim());
      
      if (result.success) {
        onClose();
      } else {
        setError(result.error || 'Failed to send message');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleReadMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!readData.secretWord || !message) {
      setError('Secret word is required to read messages');
      return;
    }

    if (!onMarkAsRead) {
      setError('Read function not available');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Re-authenticate with secret word (simplified - in real app would verify against current user)
      // For now, we'll just check if secret word is provided
      if (readData.secretWord.length < 8) {
        setError('Invalid secret word');
        setLoading(false);
        return;
      }

      // Mark message as read (this will trigger self-destruction)
      const result = await onMarkAsRead(message.id);
      
      if (result.success) {
        setMessageRevealed(true);
        // Auto-close after 10 seconds
        setTimeout(() => {
          onClose();
        }, 10000);
      } else {
        setError(result.error || 'Failed to read message');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const getVerificationBadge = (verified: boolean) => {
    if (verified) {
      return (
        <Badge variant="secondary" className="text-xs">
          <ShieldCheck className="h-3 w-3 mr-1" />
          Verified
        </Badge>
      );
    } else {
      return (
        <Badge variant="destructive" className="text-xs">
          <ShieldX className="h-3 w-3 mr-1" />
          Unverified
        </Badge>
      );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-500" />
              <span>{mode === 'send' ? 'Send Message' : 'Read Message'}</span>
            </div>
            <Badge variant="outline" className="text-xs">
              <Shield className="h-3 w-3 mr-1" />
              Quantum-Safe
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <AnimatePresence mode="wait">
          {mode === 'send' ? (
            <motion.div
              key="send"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <form onSubmit={handleSendMessage} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="recipient">Recipient Username</Label>
                  <Input
                    id="recipient"
                    type="text"
                    value={sendData.recipientUsername}
                    onChange={(e) => setSendData(prev => ({ ...prev, recipientUsername: e.target.value }))}
                    placeholder="Enter recipient's username"
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    value={sendData.messageContent}
                    onChange={(e) => setSendData(prev => ({ ...prev, messageContent: e.target.value }))}
                    placeholder="Type your message here..."
                    disabled={loading}
                    rows={4}
                    maxLength={1000}
                  />
                  <div className="text-xs text-gray-500 text-right">
                    {sendData.messageContent.length}/1000 characters
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="secret">Your Secret Word</Label>
                  <div className="relative">
                    <Input
                      id="secret"
                      type={showSecretWord ? "text" : "password"}
                      value={sendData.secretWord}
                      onChange={(e) => setSendData(prev => ({ ...prev, secretWord: e.target.value }))}
                      placeholder="Enter your secret word to send"
                      disabled={loading}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowSecretWord(!showSecretWord)}
                      disabled={loading}
                    >
                      {showSecretWord ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <div className="text-xs text-gray-600">
                    Required for authentication and message encryption
                  </div>
                </div>

                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-sm text-red-600 bg-red-50 p-3 rounded-md"
                  >
                    {error}
                  </motion.div>
                )}

                <div className="flex space-x-2">
                  <Button type="button" variant="outline" onClick={onClose} disabled={loading} className="flex-1">
                    Cancel
                  </Button>
                  <Button type="submit" disabled={loading} className="flex-1">
                    {loading ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                        <span>Sending...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Send className="h-4 w-4" />
                        <span>Send</span>
                      </div>
                    )}
                  </Button>
                </div>
              </form>

              <div className="text-xs text-gray-500 text-center bg-gray-50 p-2 rounded">
                <Trash2 className="h-3 w-3 inline mr-1" />
                Message will self-destruct when read by recipient
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="read"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              {!messageRevealed ? (
                <form onSubmit={handleReadMessage} className="space-y-4">
                  {message && (
                    <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">From: {message.senderUsername}</span>
                        {getVerificationBadge(message.verified)}
                      </div>
                      <div className="text-sm text-gray-600">
                        <Clock className="h-3 w-3 inline mr-1" />
                        {formatTimestamp(message.timestamp)}
                      </div>
                      <div className="text-sm text-orange-600 bg-orange-50 p-2 rounded">
                        ⚠️ This message will be destroyed after reading
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="read-secret">Your Secret Word</Label>
                    <div className="relative">
                      <Input
                        id="read-secret"
                        type={showSecretWord ? "text" : "password"}
                        value={readData.secretWord}
                        onChange={(e) => setReadData(prev => ({ ...prev, secretWord: e.target.value }))}
                        placeholder="Enter your secret word to read"
                        disabled={loading}
                        autoFocus
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowSecretWord(!showSecretWord)}
                        disabled={loading}
                      >
                        {showSecretWord ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <div className="text-xs text-gray-600">
                      Required to decrypt and read the message
                    </div>
                  </div>

                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-red-600 bg-red-50 p-3 rounded-md"
                    >
                      {error}
                    </motion.div>
                  )}

                  <div className="flex space-x-2">
                    <Button type="button" variant="outline" onClick={onClose} disabled={loading} className="flex-1">
                      Cancel
                    </Button>
                    <Button type="submit" disabled={loading} className="flex-1">
                      {loading ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                          <span>Reading...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <Eye className="h-4 w-4" />
                          <span>Read Message</span>
                        </div>
                      )}
                    </Button>
                  </div>
                </form>
              ) : (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="space-y-4"
                >
                  {message && (
                    <>
                      <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-green-800">From: {message.senderUsername}</span>
                          {getVerificationBadge(message.verified)}
                        </div>
                        <div className="text-sm text-green-600 mb-3">
                          <Clock className="h-3 w-3 inline mr-1" />
                          {formatTimestamp(message.timestamp)}
                        </div>
                        <div className="bg-white p-3 rounded border border-green-200">
                          <p className="text-gray-900 whitespace-pre-wrap">{message.content}</p>
                        </div>
                      </div>

                      <div className="text-center text-sm text-red-600 bg-red-50 p-3 rounded-lg">
                        <Trash2 className="h-4 w-4 inline mr-1" />
                        This message has been destroyed and will close automatically
                      </div>

                      <Button onClick={onClose} className="w-full">
                        Close
                      </Button>
                    </>
                  )}
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
