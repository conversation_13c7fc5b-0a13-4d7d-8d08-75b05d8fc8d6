import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  User, 
  Calendar,
  Hash,
  MessageSquare,
  Plus,
  RefreshCw
} from 'lucide-react';
import { adminService, PendingInvite } from '@/services/admin.service';
import { formatDistanceToNow } from 'date-fns';

interface PendingInvitesPanelProps {
  invites: PendingInvite[];
  onRefresh: () => void;
  loading: boolean;
}

export function PendingInvitesPanel({ invites, onRefresh, loading }: PendingInvitesPanelProps) {
  const [processingInvite, setProcessingInvite] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [createForm, setCreateForm] = useState({
    maxUses: 1,
    expiresInDays: 7,
    notes: ''
  });

  const handleApproveInvite = async (inviteId: string, adminNotes?: string) => {
    setProcessingInvite(inviteId);
    try {
      const result = await adminService.manageInviteRequest(inviteId, 'approve', adminNotes);
      if (result.success) {
        onRefresh();
      } else {
        console.error('Failed to approve invite:', result.error);
      }
    } catch (error) {
      console.error('Error approving invite:', error);
    } finally {
      setProcessingInvite(null);
    }
  };

  const handleRejectInvite = async (inviteId: string, adminNotes?: string) => {
    setProcessingInvite(inviteId);
    try {
      const result = await adminService.manageInviteRequest(inviteId, 'reject', adminNotes);
      if (result.success) {
        onRefresh();
      } else {
        console.error('Failed to reject invite:', result.error);
      }
    } catch (error) {
      console.error('Error rejecting invite:', error);
    } finally {
      setProcessingInvite(null);
    }
  };

  const handleCreateInvite = async () => {
    setCreateLoading(true);
    try {
      const result = await adminService.createInviteCode({
        maxUses: createForm.maxUses,
        expiresInDays: createForm.expiresInDays,
        notes: createForm.notes
      });

      if (result.success) {
        setShowCreateDialog(false);
        setCreateForm({ maxUses: 1, expiresInDays: 7, notes: '' });
        onRefresh();
      } else {
        console.error('Failed to create invite:', result.error);
      }
    } catch (error) {
      console.error('Error creating invite:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Pending Invite Requests</h3>
          <p className="text-sm text-muted-foreground">
            Review and manage invite code requests from users
          </p>
        </div>
        <div className="flex space-x-2">
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Invite
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Invite Code</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="maxUses">Maximum Uses</Label>
                  <Input
                    id="maxUses"
                    type="number"
                    min="1"
                    max="100"
                    value={createForm.maxUses}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, maxUses: parseInt(e.target.value) || 1 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="expiresInDays">Expires In (Days)</Label>
                  <Input
                    id="expiresInDays"
                    type="number"
                    min="1"
                    max="365"
                    value={createForm.expiresInDays}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, expiresInDays: parseInt(e.target.value) || 7 }))}
                  />
                </div>
                <div>
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Purpose or notes for this invite..."
                    value={createForm.notes}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, notes: e.target.value }))}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateInvite} disabled={createLoading}>
                    {createLoading ? 'Creating...' : 'Create Invite'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          <Button onClick={onRefresh} disabled={loading} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {invites.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Pending Invites</h3>
            <p className="text-muted-foreground text-center">
              All invite requests have been processed. New requests will appear here.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {invites.map((invite, index) => (
            <motion.div
              key={invite.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <Hash className="h-4 w-4 text-muted-foreground" />
                        <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                          {invite.code}
                        </code>
                      </div>
                      {isExpired(invite.expiresAt) && (
                        <Badge variant="destructive">Expired</Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleApproveInvite(invite.id)}
                        disabled={processingInvite === invite.id || isExpired(invite.expiresAt)}
                      >
                        <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleRejectInvite(invite.id)}
                        disabled={processingInvite === invite.id}
                      >
                        <XCircle className="h-4 w-4 mr-1 text-red-500" />
                        Reject
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>Requested by: <strong>{invite.creatorUsername}</strong></span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>Created: {formatDate(invite.createdAt)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>Expires: {formatDate(invite.expiresAt)}</span>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex space-x-4 text-sm text-muted-foreground">
                      <span>Max Uses: {invite.maxUses}</span>
                      <span>Current Uses: {invite.currentUses}</span>
                    </div>
                    
                    {invite.metadata?.notes && (
                      <div className="flex items-center space-x-2">
                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm italic">{invite.metadata.notes}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
