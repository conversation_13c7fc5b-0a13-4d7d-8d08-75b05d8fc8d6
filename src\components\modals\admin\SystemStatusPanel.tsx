import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Database, 
  Server, 
  Users, 
  MessageSquare,
  Shield,
  Clock,
  TrendingUp,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react';
import { SystemStats } from '@/services/admin.service';

interface SystemStatusPanelProps {
  stats: SystemStats | null;
  onRefresh: () => void;
  loading: boolean;
}

interface HealthMetric {
  name: string;
  status: 'healthy' | 'warning' | 'critical';
  value: string;
  description: string;
  icon: React.ComponentType<any>;
}

export function SystemStatusPanel({ stats, onRefresh, loading }: SystemStatusPanelProps) {
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  useEffect(() => {
    if (!loading) {
      setLastRefresh(new Date());
    }
  }, [loading]);

  const getHealthMetrics = (): HealthMetric[] => {
    if (!stats) return [];

    const userActivityRate = stats.activeUsers / Math.max(stats.totalUsers, 1);
    const messageActivity = stats.messagesToday;

    return [
      {
        name: 'Database Connection',
        status: 'healthy',
        value: 'Connected',
        description: 'Database is responding normally',
        icon: Database
      },
      {
        name: 'User Activity',
        status: userActivityRate > 0.7 ? 'healthy' : userActivityRate > 0.3 ? 'warning' : 'critical',
        value: `${Math.round(userActivityRate * 100)}%`,
        description: `${stats.activeUsers} of ${stats.totalUsers} users active`,
        icon: Users
      },
      {
        name: 'Message Flow',
        status: messageActivity > 10 ? 'healthy' : messageActivity > 0 ? 'warning' : 'critical',
        value: `${messageActivity} today`,
        description: 'Messages sent in the last 24 hours',
        icon: MessageSquare
      },
      {
        name: 'Device Security',
        status: 'healthy',
        value: `${stats.activeDevices} active`,
        description: 'All devices using quantum-safe encryption',
        icon: Shield
      }
    ];
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge className="bg-green-100 text-green-800">Healthy</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">Warning</Badge>;
      case 'critical':
        return <Badge variant="destructive">Critical</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getOverallHealth = () => {
    const metrics = getHealthMetrics();
    const criticalCount = metrics.filter(m => m.status === 'critical').length;
    const warningCount = metrics.filter(m => m.status === 'warning').length;

    if (criticalCount > 0) return 'critical';
    if (warningCount > 0) return 'warning';
    return 'healthy';
  };

  const healthMetrics = getHealthMetrics();
  const overallHealth = getOverallHealth();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">System Status</h3>
          <p className="text-sm text-muted-foreground">
            Monitor system health and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-muted-foreground">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </div>
          <Button onClick={onRefresh} disabled={loading} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overall System Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Server className="h-5 w-5" />
              <span>Overall System Health</span>
            </div>
            {getStatusBadge(overallHealth)}
          </CardTitle>
          <CardDescription>
            Real-time system status and performance indicators
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {healthMetrics.map((metric, index) => (
              <motion.div
                key={metric.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 border rounded-lg"
              >
                <div className="flex items-center justify-between mb-2">
                  <metric.icon className="h-5 w-5 text-muted-foreground" />
                  {getStatusIcon(metric.status)}
                </div>
                <h4 className="font-semibold text-sm">{metric.name}</h4>
                <p className="text-lg font-bold">{metric.value}</p>
                <p className="text-xs text-muted-foreground">{metric.description}</p>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* User Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>User Statistics</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Total Users</span>
                <span className="font-semibold">{stats.totalUsers}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Active Users</span>
                <span className="font-semibold text-green-600">{stats.activeUsers}</span>
              </div>
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm">Activity Rate</span>
                  <span className="text-sm">{Math.round((stats.activeUsers / Math.max(stats.totalUsers, 1)) * 100)}%</span>
                </div>
                <Progress value={(stats.activeUsers / Math.max(stats.totalUsers, 1)) * 100} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Message Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5" />
                <span>Message Statistics</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Total Messages</span>
                <span className="font-semibold">{stats.totalMessages}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Messages Today</span>
                <span className="font-semibold text-blue-600">{stats.messagesToday}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <TrendingUp className="h-4 w-4" />
                <span>All messages use quantum-safe encryption</span>
              </div>
            </CardContent>
          </Card>

          {/* Security & Infrastructure */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Security Status</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Active Devices</span>
                <span className="font-semibold">{stats.activeDevices}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Encryption</span>
                <Badge className="bg-green-100 text-green-800">Quantum-Safe</Badge>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>All connections secured</span>
              </div>
            </CardContent>
          </Card>

          {/* Invite System */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Invite System</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Total Invites</span>
                <span className="font-semibold">{stats.totalInvites}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Pending Approval</span>
                <span className="font-semibold text-orange-600">{stats.pendingInvites}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>Invite-only registration active</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
