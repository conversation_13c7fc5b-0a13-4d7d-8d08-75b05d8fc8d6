import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  User, 
  Shield, 
  Clock, 
  Search,
  UserCheck,
  UserX,
  UserMinus,
  RefreshCw,
  Settings,
  Calendar
} from 'lucide-react';
import { adminService, AdminUser } from '@/services/admin.service';
import { formatDistanceToNow } from 'date-fns';

interface UserManagementPanelProps {
  users: AdminUser[];
  onRefresh: () => void;
  loading: boolean;
}

export function UserManagementPanel({ users, onRefresh, loading }: UserManagementPanelProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [showUserDialog, setShowUserDialog] = useState(false);
  const [updatingUser, setUpdatingUser] = useState<string | null>(null);
  const [adminNotes, setAdminNotes] = useState('');

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.displayName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    
    return matchesSearch && matchesStatus && matchesRole;
  });

  const handleUpdateUserStatus = async (userId: string, newStatus: 'active' | 'inactive' | 'suspended') => {
    setUpdatingUser(userId);
    try {
      const result = await adminService.updateUserStatus(userId, newStatus, adminNotes);
      if (result.success) {
        onRefresh();
        setShowUserDialog(false);
        setAdminNotes('');
      } else {
        console.error('Failed to update user status:', result.error);
      }
    } catch (error) {
      console.error('Error updating user status:', error);
    } finally {
      setUpdatingUser(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">
          <Shield className="h-3 w-3 mr-1" />
          Admin
        </Badge>;
      case 'super_admin':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">
          <Shield className="h-3 w-3 mr-1" />
          Super Admin
        </Badge>;
      default:
        return <Badge variant="outline">User</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  };

  const openUserDialog = (user: AdminUser) => {
    setSelectedUser(user);
    setShowUserDialog(true);
    setAdminNotes('');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">User Management</h3>
          <p className="text-sm text-muted-foreground">
            Manage user accounts and permissions
          </p>
        </div>
        <Button onClick={onRefresh} disabled={loading} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search Users</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by username or display name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="status-filter">Status Filter</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="role-filter">Role Filter</Label>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="user">Users</SelectItem>
                  <SelectItem value="admin">Admins</SelectItem>
                  <SelectItem value="super_admin">Super Admins</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <div className="grid gap-4">
        {filteredUsers.map((user, index) => (
          <motion.div
            key={user.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => openUserDialog(user)}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-10 h-10 bg-muted rounded-full">
                      <User className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold">{user.displayName}</h4>
                        {getRoleBadge(user.role)}
                        {getStatusBadge(user.status)}
                      </div>
                      <p className="text-sm text-muted-foreground">@{user.username}</p>
                    </div>
                  </div>
                  
                  <div className="text-right text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>Last seen: {formatDate(user.lastSeen)}</span>
                    </div>
                    <div className="flex items-center space-x-1 mt-1">
                      <Calendar className="h-3 w-3" />
                      <span>Joined: {formatDate(user.createdAt)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredUsers.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <User className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Users Found</h3>
            <p className="text-muted-foreground text-center">
              No users match your current filters. Try adjusting your search criteria.
            </p>
          </CardContent>
        </Card>
      )}

      {/* User Details Dialog */}
      <Dialog open={showUserDialog} onOpenChange={setShowUserDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Manage User: {selectedUser?.displayName}</span>
            </DialogTitle>
          </DialogHeader>
          
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label>Username</Label>
                  <p className="font-mono bg-muted p-2 rounded">{selectedUser.username}</p>
                </div>
                <div>
                  <Label>Display Name</Label>
                  <p className="bg-muted p-2 rounded">{selectedUser.displayName}</p>
                </div>
                <div>
                  <Label>Current Status</Label>
                  <div className="p-2">{getStatusBadge(selectedUser.status)}</div>
                </div>
                <div>
                  <Label>Role</Label>
                  <div className="p-2">{getRoleBadge(selectedUser.role)}</div>
                </div>
              </div>

              <div>
                <Label htmlFor="admin-notes">Admin Notes (Optional)</Label>
                <Textarea
                  id="admin-notes"
                  placeholder="Reason for status change..."
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowUserDialog(false)}>
                  Cancel
                </Button>
                
                {selectedUser.status !== 'active' && (
                  <Button
                    onClick={() => handleUpdateUserStatus(selectedUser.id, 'active')}
                    disabled={updatingUser === selectedUser.id}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <UserCheck className="h-4 w-4 mr-2" />
                    Activate
                  </Button>
                )}
                
                {selectedUser.status !== 'inactive' && (
                  <Button
                    variant="outline"
                    onClick={() => handleUpdateUserStatus(selectedUser.id, 'inactive')}
                    disabled={updatingUser === selectedUser.id}
                  >
                    <UserMinus className="h-4 w-4 mr-2" />
                    Deactivate
                  </Button>
                )}
                
                {selectedUser.status !== 'suspended' && selectedUser.role !== 'admin' && (
                  <Button
                    variant="destructive"
                    onClick={() => handleUpdateUserStatus(selectedUser.id, 'suspended')}
                    disabled={updatingUser === selectedUser.id}
                  >
                    <UserX className="h-4 w-4 mr-2" />
                    Suspend
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
