import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Cpu, 
  Zap, 
  Shield, 
  BarChart3, 
  RefreshCw, 
  Settings,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { cryptoService } from '@/services/crypto.service';

interface PerformanceData {
  wasm: any;
  mode: string;
  optimization: boolean;
  hardware: any;
}

interface BenchmarkResults {
  keyGeneration: { wasm: number; js: number };
  encryption: { wasm: number; js: number };
  signing: { wasm: number; js: number };
}

export function CryptoPerformanceMonitor() {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [benchmarkResults, setBenchmarkResults] = useState<BenchmarkResults | null>(null);
  const [isRunningBenchmark, setIsRunningBenchmark] = useState(false);
  const [currentMode, setCurrentMode] = useState<'balanced' | 'speed' | 'security'>('balanced');

  useEffect(() => {
    updatePerformanceData();
    const interval = setInterval(updatePerformanceData, 2000);
    return () => clearInterval(interval);
  }, []);

  const updatePerformanceData = () => {
    const data = cryptoService.getPerformanceMetrics();
    setPerformanceData(data);
    setCurrentMode(data.mode as 'balanced' | 'speed' | 'security');
  };

  const runBenchmark = async () => {
    setIsRunningBenchmark(true);
    try {
      const results = await cryptoService.benchmarkOperations();
      setBenchmarkResults(results);
    } catch (error) {
      console.error('Benchmark failed:', error);
    } finally {
      setIsRunningBenchmark(false);
    }
  };

  const resetMetrics = () => {
    cryptoService.resetPerformanceMetrics();
    updatePerformanceData();
    setBenchmarkResults(null);
  };

  const changePerformanceMode = (mode: 'balanced' | 'speed' | 'security') => {
    cryptoService.setPerformanceMode(mode);
    setCurrentMode(mode);
    updatePerformanceData();
  };

  const getModeColor = (mode: string) => {
    switch (mode) {
      case 'speed': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'security': return 'text-red-600 bg-red-50 border-red-200';
      case 'balanced': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getOptimizationStatus = () => {
    if (!performanceData) return null;
    
    if (performanceData.optimization && performanceData.hardware?.hasWebAssembly) {
      return (
        <div className="flex items-center space-x-2 text-green-600">
          <CheckCircle className="h-4 w-4" />
          <span className="text-sm font-medium">WASM Acceleration Active</span>
        </div>
      );
    } else if (performanceData.hardware?.hasWebAssembly === false) {
      return (
        <div className="flex items-center space-x-2 text-red-600">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm font-medium">WASM Not Supported</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center space-x-2 text-orange-600">
          <Info className="h-4 w-4" />
          <span className="text-sm font-medium">JS Fallback Mode</span>
        </div>
      );
    }
  };

  const formatTime = (ms: number) => {
    if (ms < 1) return `${(ms * 1000).toFixed(1)}μs`;
    if (ms < 1000) return `${ms.toFixed(1)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const calculateSpeedup = (wasmTime: number, jsTime: number) => {
    if (wasmTime === 0 || jsTime === 0) return 0;
    return ((jsTime - wasmTime) / jsTime * 100);
  };

  if (!performanceData) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <div className="animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full" />
          <span className="ml-2">Loading performance data...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Activity className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold">Crypto Performance Monitor</h2>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={resetMetrics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button 
            variant="default" 
            size="sm" 
            onClick={runBenchmark}
            disabled={isRunningBenchmark}
          >
            {isRunningBenchmark ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                <span>Running...</span>
              </div>
            ) : (
              <>
                <BarChart3 className="h-4 w-4 mr-2" />
                Benchmark
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="hardware">Hardware</TabsTrigger>
          <TabsTrigger value="benchmark">Benchmark</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Performance Mode */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Settings className="h-4 w-4 mr-2" />
                  Performance Mode
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Badge className={`${getModeColor(currentMode)} border`}>
                  {currentMode.toUpperCase()}
                </Badge>
                <p className="text-xs text-gray-600 mt-2">
                  {currentMode === 'speed' && 'Maximum performance with WASM acceleration'}
                  {currentMode === 'balanced' && 'Balanced performance and security'}
                  {currentMode === 'security' && 'Maximum security with JS implementation'}
                </p>
              </CardContent>
            </Card>

            {/* Optimization Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Zap className="h-4 w-4 mr-2" />
                  Acceleration
                </CardTitle>
              </CardHeader>
              <CardContent>
                {getOptimizationStatus()}
              </CardContent>
            </Card>

            {/* Security Level */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Shield className="h-4 w-4 mr-2" />
                  Security Level
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">Post-Quantum Safe</span>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  CRYSTALS-Kyber + Dilithium
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Real-time Metrics */}
          {performanceData.wasm && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Real-time Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatTime(performanceData.wasm.keyGeneration)}
                    </div>
                    <div className="text-xs text-gray-600">Key Generation</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {formatTime(performanceData.wasm.encryption)}
                    </div>
                    <div className="text-xs text-gray-600">Encryption</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {formatTime(performanceData.wasm.decryption)}
                    </div>
                    <div className="text-xs text-gray-600">Decryption</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {formatTime(performanceData.wasm.signing)}
                    </div>
                    <div className="text-xs text-gray-600">Signing</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {formatTime(performanceData.wasm.verification)}
                    </div>
                    <div className="text-xs text-gray-600">Verification</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="hardware" className="space-y-4">
          {performanceData.hardware && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Cpu className="h-5 w-5 mr-2" />
                  Hardware Capabilities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">WebAssembly</span>
                      <Badge variant={performanceData.hardware.hasWebAssembly ? "default" : "secondary"}>
                        {performanceData.hardware.hasWebAssembly ? "Supported" : "Not Supported"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">SIMD</span>
                      <Badge variant={performanceData.hardware.hasSIMD ? "default" : "secondary"}>
                        {performanceData.hardware.hasSIMD ? "Available" : "Not Available"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">SharedArrayBuffer</span>
                      <Badge variant={performanceData.hardware.hasSharedArrayBuffer ? "default" : "secondary"}>
                        {performanceData.hardware.hasSharedArrayBuffer ? "Available" : "Not Available"}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Atomics</span>
                      <Badge variant={performanceData.hardware.hasAtomics ? "default" : "secondary"}>
                        {performanceData.hardware.hasAtomics ? "Available" : "Not Available"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Web Workers</span>
                      <Badge variant={performanceData.hardware.workerSupport ? "default" : "secondary"}>
                        {performanceData.hardware.workerSupport ? "Supported" : "Not Supported"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Memory Limit</span>
                      <span className="text-sm font-medium">
                        {Math.round(performanceData.hardware.memoryLimit / 1024 / 1024)}MB
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="benchmark" className="space-y-4">
          {benchmarkResults && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Key Generation</span>
                      <span className="text-xs text-gray-600">
                        {calculateSpeedup(benchmarkResults.keyGeneration.wasm, benchmarkResults.keyGeneration.js).toFixed(1)}% faster
                      </span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs">
                        <span>WASM: {formatTime(benchmarkResults.keyGeneration.wasm)}</span>
                        <span>JS: {formatTime(benchmarkResults.keyGeneration.js)}</span>
                      </div>
                      <Progress 
                        value={benchmarkResults.keyGeneration.wasm > 0 ? 
                          (benchmarkResults.keyGeneration.wasm / benchmarkResults.keyGeneration.js) * 100 : 0} 
                        className="h-2" 
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Performance Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-3 block">Performance Mode</label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {(['speed', 'balanced', 'security'] as const).map((mode) => (
                      <Button
                        key={mode}
                        variant={currentMode === mode ? "default" : "outline"}
                        onClick={() => changePerformanceMode(mode)}
                        className="justify-start"
                      >
                        <div className="text-left">
                          <div className="font-medium capitalize">{mode}</div>
                          <div className="text-xs opacity-70">
                            {mode === 'speed' && 'Max WASM acceleration'}
                            {mode === 'balanced' && 'Balanced performance'}
                            {mode === 'security' && 'Pure JS implementation'}
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
