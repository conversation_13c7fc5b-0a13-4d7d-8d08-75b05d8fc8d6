import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

export interface CircleState {
  type: 'guest' | 'authenticated' | 'new-message' | 'reading' | 'sending';
  messageCount?: number;
  isLoading?: boolean;
}

interface InteractiveCircleProps {
  state: CircleState;
  onTap: () => void;
  onLongPress: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
  xl: 'w-48 h-48'
};

const getCircleColor = (state: CircleState) => {
  switch (state.type) {
    case 'guest':
      return 'bg-red-500 hover:bg-red-600';
    case 'authenticated':
      return 'bg-blue-500 hover:bg-blue-600';
    case 'new-message':
      return 'bg-green-500 hover:bg-green-600';
    case 'reading':
      return 'bg-purple-500 hover:bg-purple-600';
    case 'sending':
      return 'bg-orange-500 hover:bg-orange-600';
    default:
      return 'bg-gray-500 hover:bg-gray-600';
  }
};

const getStateText = (state: CircleState) => {
  switch (state.type) {
    case 'guest':
      return 'Tap to Sign In';
    case 'authenticated':
      return 'Tap to Send';
    case 'new-message':
      return `${state.messageCount || 1} New Message${(state.messageCount || 1) > 1 ? 's' : ''}`;
    case 'reading':
      return 'Reading...';
    case 'sending':
      return 'Sending...';
    default:
      return '';
  }
};

export function InteractiveCircle({
  state,
  onTap,
  onLongPress,
  className,
  size = 'xl',
  disabled = false
}: InteractiveCircleProps) {
  const [isPressed, setIsPressed] = useState(false);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);

  const handleMouseDown = () => {
    if (disabled) return;
    
    setIsPressed(true);
    const timer = setTimeout(() => {
      onLongPress();
      setIsPressed(false);
    }, 800); // 800ms for long press
    setLongPressTimer(timer);
  };

  const handleMouseUp = () => {
    if (disabled) return;
    
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
    
    if (isPressed) {
      onTap();
    }
    setIsPressed(false);
  };

  const handleMouseLeave = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
    setIsPressed(false);
  };

  useEffect(() => {
    return () => {
      if (longPressTimer) {
        clearTimeout(longPressTimer);
      }
    };
  }, [longPressTimer]);

  const pulseAnimation = state.type === 'new-message' ? {
    scale: [1, 1.1, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  } : {};

  const loadingAnimation = state.isLoading ? {
    rotate: 360,
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "linear"
    }
  } : {};

  return (
    <div className={cn("relative flex items-center justify-center", className)}>
      {/* Outer glow ring for new messages */}
      <AnimatePresence>
        {state.type === 'new-message' && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ 
              opacity: [0.3, 0.6, 0.3],
              scale: [1, 1.2, 1]
            }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className={cn(
              "absolute rounded-full border-4 border-green-400",
              sizeClasses[size]
            )}
            style={{ width: '120%', height: '120%' }}
          />
        )}
      </AnimatePresence>

      {/* Main circle */}
      <motion.button
        className={cn(
          "relative rounded-full shadow-lg transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-opacity-50",
          sizeClasses[size],
          getCircleColor(state),
          isPressed && "scale-95",
          disabled && "opacity-50 cursor-not-allowed",
          state.type === 'guest' && "focus:ring-red-300",
          state.type === 'authenticated' && "focus:ring-blue-300",
          state.type === 'new-message' && "focus:ring-green-300",
          state.type === 'reading' && "focus:ring-purple-300",
          state.type === 'sending' && "focus:ring-orange-300"
        )}
        animate={{
          ...pulseAnimation,
          ...loadingAnimation
        }}
        whileHover={{ scale: disabled ? 1 : 1.05 }}
        whileTap={{ scale: disabled ? 1 : 0.95 }}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleMouseDown}
        onTouchEnd={handleMouseUp}
        disabled={disabled}
      >
        {/* Plain circle - no content */}
        <div className="flex items-center justify-center h-full">
          {/* Keep only message count indicator for new messages */}
          {state.type === 'new-message' && state.messageCount && state.messageCount > 1 && (
            <span className="bg-white text-green-600 text-sm font-bold rounded-full w-8 h-8 flex items-center justify-center">
              {state.messageCount > 9 ? '9+' : state.messageCount}
            </span>
          )}
        </div>

        {/* Loading spinner overlay */}
        <AnimatePresence>
          {state.isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 rounded-full"
            >
              <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Long press indicator */}
      <AnimatePresence>
        {isPressed && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            className="absolute inset-0 rounded-full border-4 border-white border-opacity-50"
            style={{
              width: '110%',
              height: '110%'
            }}
          />
        )}
      </AnimatePresence>
    </div>
  );
}
