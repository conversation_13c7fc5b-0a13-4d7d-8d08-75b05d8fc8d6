import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authService, AuthSession, AuthUser, AuthDevice } from '@/services/auth.service';
import { inviteService } from '@/services/invite.service';
import { messageLifecycleService } from '@/services/messageLifecycle.service';

interface AuthContextType {
  // State
  isAuthenticated: boolean;
  isLoading: boolean;
  user: AuthUser | null;
  device: AuthDevice | null;
  session: AuthSession | null;
  
  // Actions
  signIn: (username: string, secretWord: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (username: string, secretWord: string, inviteCode: string, displayName?: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [device, setDevice] = useState<AuthDevice | null>(null);
  const [session, setSession] = useState<AuthSession | null>(null);

  // Initialize authentication state and services
  useEffect(() => {
    initializeAuth();
    initializeServices();
  }, []);

  const initializeServices = async () => {
    try {
      // Initialize message lifecycle service with default config
      await messageLifecycleService.initialize({
        defaultExpirationMinutes: 60, // 1 hour default
        maxExpirationMinutes: 1440, // 24 hours max
        cleanupIntervalMinutes: 5, // Check every 5 minutes
        autoDestroyOnRead: true,
        secureDeletePasses: 3
      });

      console.log('Message lifecycle service initialized');
    } catch (error) {
      console.error('Failed to initialize services:', error);
    }
  };

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      
      // Check for existing session
      const existingSession = authService.getCurrentSession();
      
      if (existingSession && authService.isAuthenticated()) {
        setSession(existingSession);
        setUser(existingSession.user);
        setDevice(existingSession.device);
        setIsAuthenticated(true);
        
        // Try to refresh the session to ensure it's still valid
        const refreshed = await authService.refreshSession();
        if (!refreshed) {
          // Session is invalid, clear state
          clearAuthState();
        }
      } else {
        clearAuthState();
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      clearAuthState();
    } finally {
      setIsLoading(false);
    }
  };

  const signIn = async (username: string, secretWord: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      const result = await authService.authenticate(username, secretWord);
      
      if (result.success && result.session) {
        setSession(result.session);
        setUser(result.session.user);
        setDevice(result.session.device);
        setIsAuthenticated(true);
        
        return { success: true };
      } else {
        clearAuthState();
        return { success: false, error: result.error || 'Authentication failed' };
      }
    } catch (error) {
      console.error('Sign in error:', error);
      clearAuthState();
      return { success: false, error: 'Authentication failed' };
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (
    username: string,
    secretWord: string,
    inviteCode: string,
    displayName?: string
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);

      // First validate the invite code
      const inviteValidation = await inviteService.validateInvite(inviteCode);
      if (!inviteValidation.isValid) {
        return { success: false, error: inviteValidation.error || 'Invalid invite code' };
      }

      // Create the user account
      const result = await authService.createUser(username, secretWord, displayName);

      if (result.success && result.userId) {
        // Use the invite code
        const inviteUseResult = await inviteService.useInvite(inviteCode, result.userId);
        if (!inviteUseResult.success) {
          console.warn('Failed to mark invite as used:', inviteUseResult.error);
          // Don't fail the signup for this, just log it
        }

        // After successful registration, automatically sign in
        const signInResult = await signIn(username, secretWord);
        return signInResult;
      } else {
        return { success: false, error: result.error || 'Registration failed' };
      }
    } catch (error) {
      console.error('Sign up error:', error);
      return { success: false, error: 'Registration failed' };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authService.signOut();
      clearAuthState();
    } catch (error) {
      console.error('Sign out error:', error);
      clearAuthState();
    } finally {
      setIsLoading(false);
    }
  };

  const refreshSession = async (): Promise<boolean> => {
    try {
      const refreshed = await authService.refreshSession();
      
      if (refreshed) {
        const updatedSession = authService.getCurrentSession();
        if (updatedSession) {
          setSession(updatedSession);
          setUser(updatedSession.user);
          setDevice(updatedSession.device);
          setIsAuthenticated(true);
        }
        return true;
      } else {
        clearAuthState();
        return false;
      }
    } catch (error) {
      console.error('Session refresh error:', error);
      clearAuthState();
      return false;
    }
  };

  const clearAuthState = () => {
    setSession(null);
    setUser(null);
    setDevice(null);
    setIsAuthenticated(false);
  };

  const value: AuthContextType = {
    // State
    isAuthenticated,
    isLoading,
    user,
    device,
    session,
    
    // Actions
    signIn,
    signUp,
    signOut,
    refreshSession
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook for checking authentication status
export function useAuthStatus() {
  const { isAuthenticated, isLoading } = useAuth();
  return { isAuthenticated, isLoading };
}

// Hook for getting current user
export function useCurrentUser() {
  const { user, device, isAuthenticated } = useAuth();
  return { user, device, isAuthenticated };
}

// Hook for authentication actions
export function useAuthActions() {
  const { signIn, signUp, signOut, refreshSession } = useAuth();
  return { signIn, signUp, signOut, refreshSession };
}
