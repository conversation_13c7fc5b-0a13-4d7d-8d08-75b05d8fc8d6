import { useState, useEffect, useCallback } from 'react';
import { messagingService, Message, SendMessageOptions } from '@/services/messaging.service';
import { realtimeService, TypingIndicator, PresenceUser, MessageDeliveryStatus } from '@/services/realtime.service';
import { messageLifecycleService } from '@/services/messageLifecycle.service';
import { useAuth } from '@/contexts/AuthContext';

export interface UseMessagingOptions {
  recipientId?: string;
  groupId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseMessagingReturn {
  messages: Message[];
  loading: boolean;
  error: string | null;
  sendMessage: (content: string, options?: { expirationMinutes?: number }) => Promise<{ success: boolean; error?: string }>;
  markAsRead: (messageId: string) => Promise<{ success: boolean; error?: string }>;
  refreshMessages: () => Promise<void>;
  participants: Array<{
    id: string;
    username: string;
    displayName: string;
    status: string;
  }>;
  // Real-time features
  typingUsers: TypingIndicator[];
  onlineUsers: PresenceUser[];
  connectionState: 'connected' | 'disconnected' | 'reconnecting';
  messageStatuses: Map<string, MessageDeliveryStatus>;
  sendTypingIndicator: (isTyping: boolean) => Promise<void>;
}

/**
 * React hook for messaging functionality with post-quantum cryptography
 */
export function useMessaging(options: UseMessagingOptions = {}): UseMessagingReturn {
  const { isAuthenticated } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [participants, setParticipants] = useState<Array<{
    id: string;
    username: string;
    displayName: string;
    status: string;
  }>>([]);

  // Real-time state
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<PresenceUser[]>([]);
  const [connectionState, setConnectionState] = useState<'connected' | 'disconnected' | 'reconnecting'>('disconnected');
  const [messageStatuses, setMessageStatuses] = useState<Map<string, MessageDeliveryStatus>>(new Map());

  const {
    recipientId,
    groupId,
    autoRefresh = true,
    refreshInterval = 5000
  } = options;

  /**
   * Load messages from the messaging service
   */
  const loadMessages = useCallback(async () => {
    if (!isAuthenticated) {
      setMessages([]);
      setParticipants([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const [messagesData, participantsData] = await Promise.all([
        messagingService.getMessages({
          recipientId,
          groupId,
          limit: 50
        }),
        messagingService.getConversationParticipants(recipientId, groupId)
      ]);

      setMessages(messagesData);
      setParticipants(participantsData);
    } catch (err) {
      console.error('Failed to load messages:', err);
      setError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, recipientId, groupId]);

  /**
   * Send typing indicator
   */
  const sendTypingIndicator = useCallback(async (isTyping: boolean): Promise<void> => {
    if (!isAuthenticated) return;

    const conversationId = recipientId || groupId;
    if (!conversationId) return;

    try {
      await realtimeService.sendTypingIndicator(conversationId, isTyping);
    } catch (error) {
      console.error('Failed to send typing indicator:', error);
    }
  }, [isAuthenticated, recipientId, groupId]);

  /**
   * Send a new message with optional expiration
   */
  const sendMessage = useCallback(async (
    content: string,
    options?: { expirationMinutes?: number }
  ): Promise<{ success: boolean; error?: string }> => {
    if (!isAuthenticated) {
      return { success: false, error: 'Not authenticated' };
    }

    if (!content.trim()) {
      return { success: false, error: 'Message content cannot be empty' };
    }

    try {
      const sendOptions: SendMessageOptions = {
        content: content.trim(),
        expirationMinutes: options?.expirationMinutes
      };

      if (recipientId) {
        sendOptions.recipientId = recipientId;
      } else if (groupId) {
        sendOptions.groupId = groupId;
      } else {
        return { success: false, error: 'No recipient specified' };
      }

      const result = await messagingService.sendMessage(sendOptions);

      if (result.success) {
        // Clear typing indicator
        await sendTypingIndicator(false);
        // Refresh messages to show the new message
        await loadMessages();
      }

      return result;
    } catch (err) {
      console.error('Failed to send message:', err);
      return { success: false, error: 'Failed to send message' };
    }
  }, [isAuthenticated, recipientId, groupId, loadMessages]);

  /**
   * Mark a message as read
   */
  const markAsRead = useCallback(async (messageId: string): Promise<{ success: boolean; error?: string }> => {
    if (!isAuthenticated) {
      return { success: false, error: 'Not authenticated' };
    }

    try {
      const result = await messagingService.markMessageAsRead(messageId);

      if (result.success) {
        // Update local state to reflect the read status
        setMessages(prevMessages => 
          prevMessages.map(msg => 
            msg.id === messageId 
              ? { ...msg, status: 'read' as const }
              : msg
          )
        );
      }

      return result;
    } catch (err) {
      console.error('Failed to mark message as read:', err);
      return { success: false, error: 'Failed to mark message as read' };
    }
  }, [isAuthenticated]);

  /**
   * Refresh messages manually
   */
  const refreshMessages = useCallback(async () => {
    await loadMessages();
  }, [loadMessages]);

  // Load messages on mount and when dependencies change
  useEffect(() => {
    loadMessages();
  }, [loadMessages]);

  // Initialize real-time messaging
  useEffect(() => {
    if (!isAuthenticated) {
      return;
    }

    const initializeRealtime = async () => {
      try {
        await realtimeService.initialize({
          onNewMessage: (message) => {
            setMessages(prev => [...prev, message]);
          },
          onMessageStatusUpdate: (status) => {
            setMessageStatuses(prev => new Map(prev.set(status.messageId, status)));
          },
          onTypingIndicator: (indicator) => {
            setTypingUsers(prev => {
              const filtered = prev.filter(u => u.userId !== indicator.userId);
              return indicator.isTyping ? [...filtered, indicator] : filtered;
            });
          },
          onPresenceUpdate: (users) => {
            setOnlineUsers(users);
          },
          onConnectionStateChange: (state) => {
            setConnectionState(state);
          }
        });

        // Subscribe to typing indicators for this conversation
        const conversationId = recipientId || groupId;
        if (conversationId) {
          await realtimeService.subscribeToTypingIndicators(conversationId);
        }
      } catch (error) {
        console.error('Failed to initialize real-time messaging:', error);
        setError('Failed to connect to real-time messaging');
      }
    };

    initializeRealtime();

    return () => {
      realtimeService.cleanup();
    };
  }, [isAuthenticated, recipientId, groupId]);

  // Set up auto-refresh if enabled (fallback for when real-time fails)
  useEffect(() => {
    if (!autoRefresh || !isAuthenticated || connectionState === 'connected') {
      return;
    }

    const interval = setInterval(() => {
      loadMessages();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, isAuthenticated, connectionState, loadMessages]);

  return {
    messages,
    loading,
    error,
    sendMessage,
    markAsRead,
    refreshMessages,
    participants,
    // Real-time features
    typingUsers,
    onlineUsers,
    connectionState,
    messageStatuses,
    sendTypingIndicator
  };
}

/**
 * Hook for getting conversation list
 */
export function useConversations() {
  const { isAuthenticated } = useAuth();
  const [conversations, setConversations] = useState<Array<{
    id: string;
    type: 'direct' | 'group';
    name: string;
    lastMessage?: {
      content: string;
      timestamp: string;
      senderUsername: string;
    };
    unreadCount: number;
    participants: string[];
  }>>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadConversations = useCallback(async () => {
    if (!isAuthenticated) {
      setConversations([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // This would need to be implemented in the messaging service
      // For now, return empty array
      setConversations([]);
    } catch (err) {
      console.error('Failed to load conversations:', err);
      setError('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  return {
    conversations,
    loading,
    error,
    refreshConversations: loadConversations
  };
}

/**
 * Hook for real-time message updates (now integrated into useMessaging)
 * @deprecated Use useMessaging directly - real-time features are now built-in
 */
export function useRealtimeMessages(options: UseMessagingOptions = {}) {
  return useMessaging(options);
}
