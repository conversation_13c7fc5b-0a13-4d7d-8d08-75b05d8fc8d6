@tailwind base;
@tailwind components;
@tailwind utilities;

/* Quantum-Secure Chat Design System - All colors MUST be HSL */

@layer base {
  :root {
    /* Quantum-inspired dark color palette */
    --background: 220 25% 8%;
    --foreground: 210 100% 95%;

    --card: 220 20% 10%;
    --card-foreground: 210 100% 95%;

    --popover: 220 25% 8%;
    --popover-foreground: 210 100% 95%;

    /* Quantum primary - deep blue with cyan accent */
    --primary: 240 100% 60%;
    --primary-foreground: 220 25% 8%;
    --primary-glow: 195 100% 70%;

    /* Quantum secondary - deep purple */
    --secondary: 265 85% 15%;
    --secondary-foreground: 210 100% 95%;

    /* Muted quantum tones */
    --muted: 220 15% 15%;
    --muted-foreground: 210 20% 65%;

    /* Accent - quantum cyan */
    --accent: 195 100% 50%;
    --accent-foreground: 220 25% 8%;

    /* Quantum-resistant security indicators */
    --quantum-secure: 120 100% 50%;
    --quantum-warning: 45 100% 60%;
    --quantum-danger: 0 85% 60%;

    --destructive: 0 85% 60%;
    --destructive-foreground: 210 100% 95%;

    --border: 220 15% 20%;
    --input: 220 15% 15%;
    --ring: 195 100% 70%;

    --radius: 0.5rem;

    /* Quantum gradients and effects */
    --gradient-quantum: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-secure: linear-gradient(180deg, hsl(var(--quantum-secure) / 0.1), transparent);
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(220 25% 12%));
    
    /* Quantum shadows and glows */
    --shadow-quantum: 0 0 20px hsl(var(--primary-glow) / 0.3);
    --shadow-secure: 0 0 15px hsl(var(--quantum-secure) / 0.2);
    --shadow-card: 0 8px 32px hsl(220 25% 5% / 0.8);
    
    /* Quantum animations */
    --transition-quantum: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-glow: all 0.5s ease-in-out;

    /* Sidebar quantum theme */
    --sidebar-background: 220 20% 6%;
    --sidebar-foreground: 210 100% 95%;
    --sidebar-primary: 240 100% 60%;
    --sidebar-primary-foreground: 220 25% 8%;
    --sidebar-accent: 220 15% 12%;
    --sidebar-accent-foreground: 210 100% 95%;
    --sidebar-border: 220 15% 18%;
    --sidebar-ring: 195 100% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}