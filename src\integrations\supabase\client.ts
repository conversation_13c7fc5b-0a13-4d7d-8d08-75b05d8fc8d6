// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://qutfsplxtzzbmjqznsui.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF1dGZzcGx4dHp6Ym1qcXpuc3VpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM2NDAzNDksImV4cCI6MjA2OTIxNjM0OX0.PcoRqyc9WDk6G_rrP_mN_ThYQkyuz76SdvQr-ilWnGw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});