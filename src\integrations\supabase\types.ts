export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      audit_log: {
        Row: {
          id: string
          user_id: string | null
          device_id: string | null
          action: string
          resource_type: string | null
          resource_id: string | null
          metadata: Json | null
          ip_address: string | null
          user_agent: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          user_id?: string | null
          device_id?: string | null
          action: string
          resource_type?: string | null
          resource_id?: string | null
          metadata?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string | null
          device_id?: string | null
          action?: string
          resource_type?: string | null
          resource_id?: string | null
          metadata?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_log_device_id_fkey"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audit_log_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      devices: {
        Row: {
          id: string
          user_id: string
          device_name: string
          device_fingerprint: string
          public_key_dilithium: string
          public_key_kyber: string
          status: Database["public"]["Enums"]["device_status"] | null
          last_used: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          device_name: string
          device_fingerprint: string
          public_key_dilithium: string
          public_key_kyber: string
          status?: Database["public"]["Enums"]["device_status"] | null
          last_used?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          device_name?: string
          device_fingerprint?: string
          public_key_dilithium?: string
          public_key_kyber?: string
          status?: Database["public"]["Enums"]["device_status"] | null
          last_used?: string | null
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "devices_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      group_members: {
        Row: {
          id: string
          group_id: string
          user_id: string
          role: Database["public"]["Enums"]["group_role"] | null
          joined_at: string | null
        }
        Insert: {
          id?: string
          group_id: string
          user_id: string
          role?: Database["public"]["Enums"]["group_role"] | null
          joined_at?: string | null
        }
        Update: {
          id?: string
          group_id?: string
          user_id?: string
          role?: Database["public"]["Enums"]["group_role"] | null
          joined_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "group_members_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      groups: {
        Row: {
          id: string
          name: string
          description: string | null
          created_by: string
          group_key_kyber: string
          max_members: number | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_by: string
          group_key_kyber: string
          max_members?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_by?: string
          group_key_kyber?: string
          max_members?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "groups_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      invite_codes: {
        Row: {
          id: string
          code: string
          created_by: string
          group_id: string | null
          max_uses: number | null
          current_uses: number | null
          expires_at: string
          created_at: string | null
        }
        Insert: {
          id?: string
          code: string
          created_by: string
          group_id?: string | null
          max_uses?: number | null
          current_uses?: number | null
          expires_at: string
          created_at?: string | null
        }
        Update: {
          id?: string
          code?: string
          created_by?: string
          group_id?: string | null
          max_uses?: number | null
          current_uses?: number | null
          expires_at?: string
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invite_codes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invite_codes_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          }
        ]
      }
      key_exchange_sessions: {
        Row: {
          id: string
          initiator_id: string
          responder_id: string
          initiator_device_id: string
          responder_device_id: string | null
          public_key_share: string
          encrypted_shared_secret: string | null
          session_signature: string
          status: string | null
          expires_at: string
          created_at: string | null
        }
        Insert: {
          id?: string
          initiator_id: string
          responder_id: string
          initiator_device_id: string
          responder_device_id?: string | null
          public_key_share: string
          encrypted_shared_secret?: string | null
          session_signature: string
          status?: string | null
          expires_at?: string
          created_at?: string | null
        }
        Update: {
          id?: string
          initiator_id?: string
          responder_id?: string
          initiator_device_id?: string
          responder_device_id?: string | null
          public_key_share?: string
          encrypted_shared_secret?: string | null
          session_signature?: string
          status?: string | null
          expires_at?: string
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "key_exchange_sessions_initiator_device_id_fkey"
            columns: ["initiator_device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "key_exchange_sessions_initiator_id_fkey"
            columns: ["initiator_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "key_exchange_sessions_responder_device_id_fkey"
            columns: ["responder_device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "key_exchange_sessions_responder_id_fkey"
            columns: ["responder_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      message_recipients: {
        Row: {
          id: string
          message_id: string
          recipient_id: string
          status: Database["public"]["Enums"]["message_status"] | null
          delivered_at: string | null
          read_at: string | null
        }
        Insert: {
          id?: string
          message_id: string
          recipient_id: string
          status?: Database["public"]["Enums"]["message_status"] | null
          delivered_at?: string | null
          read_at?: string | null
        }
        Update: {
          id?: string
          message_id?: string
          recipient_id?: string
          status?: Database["public"]["Enums"]["message_status"] | null
          delivered_at?: string | null
          read_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "message_recipients_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_recipients_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      messages: {
        Row: {
          id: string
          sender_id: string
          sender_device_id: string
          group_id: string | null
          recipient_id: string | null
          encrypted_content: string
          content_signature: string
          encryption_metadata: Json
          status: Database["public"]["Enums"]["message_status"] | null
          expires_at: string
          read_at: string | null
          destroyed_at: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          sender_id: string
          sender_device_id: string
          group_id?: string | null
          recipient_id?: string | null
          encrypted_content: string
          content_signature: string
          encryption_metadata: Json
          status?: Database["public"]["Enums"]["message_status"] | null
          expires_at: string
          read_at?: string | null
          destroyed_at?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          sender_id?: string
          sender_device_id?: string
          group_id?: string | null
          recipient_id?: string | null
          encrypted_content?: string
          content_signature?: string
          encryption_metadata?: Json
          status?: Database["public"]["Enums"]["message_status"] | null
          expires_at?: string
          read_at?: string | null
          destroyed_at?: string | null
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_sender_device_id_fkey"
            columns: ["sender_device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          username: string
          secret_word_hash: string
          display_name: string | null
          status: Database["public"]["Enums"]["user_status"] | null
          last_seen: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          username: string
          secret_word_hash: string
          display_name?: string | null
          status?: Database["public"]["Enums"]["user_status"] | null
          last_seen?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          username?: string
          secret_word_hash?: string
          display_name?: string | null
          status?: Database["public"]["Enums"]["user_status"] | null
          last_seen?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      authenticate_user: {
        Args: {
          p_username: string
          p_secret_word: string
        }
        Returns: {
          user_id: string
          success: boolean
        }[]
      }
      cleanup_destroyed_messages: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_group: {
        Args: {
          p_name: string
          p_description: string
          p_created_by: string
          p_group_key_kyber: string
          p_max_members?: number
        }
        Returns: string
      }
      create_user_with_secret: {
        Args: {
          p_username: string
          p_secret_word: string
          p_display_name?: string
        }
        Returns: string
      }
      destroy_expired_messages: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      generate_invite_code: {
        Args: {
          p_created_by: string
          p_group_id?: string
          p_max_uses?: number
          p_expires_in_hours?: number
        }
        Returns: string
      }
      mark_message_read: {
        Args: {
          p_message_id: string
          p_user_id: string
        }
        Returns: boolean
      }
      register_device: {
        Args: {
          p_user_id: string
          p_device_name: string
          p_device_fingerprint: string
          p_public_key_dilithium: string
          p_public_key_kyber: string
        }
        Returns: string
      }
      send_message: {
        Args: {
          p_sender_id: string
          p_sender_device_id: string
          p_group_id?: string
          p_recipient_id?: string
          p_encrypted_content: string
          p_content_signature: string
          p_encryption_metadata: Json
          p_expires_in_minutes?: number
        }
        Returns: string
      }
      use_invite_code: {
        Args: {
          p_code: string
          p_user_id: string
        }
        Returns: Json
      }
    }
    Enums: {
      device_status: "active" | "inactive" | "revoked"
      group_role: "owner" | "admin" | "member"
      message_status: "sending" | "sent" | "delivered" | "read" | "expired" | "destroyed"
      user_status: "active" | "inactive" | "suspended"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
