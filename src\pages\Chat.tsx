import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ChatInterface } from '@/components/chat/ChatInterface';
import { useAuth } from '@/contexts/AuthContext';
import { keyManagerService } from '@/services/keyManager.service';
import { cryptoService } from '@/services/crypto.service';
import { supabase } from '@/integrations/supabase/client';
import { Shield, Key, Users, MessageSquare, TestTube } from 'lucide-react';

export default function Chat() {
  const { user, device } = useAuth();
  const [selectedRecipient, setSelectedRecipient] = useState<string | null>(null);
  const [selectedRecipientName, setSelectedRecipientName] = useState<string>('');
  const [availableUsers, setAvailableUsers] = useState<Array<{
    id: string;
    username: string;
    displayName: string;
  }>>([]);
  const [deviceKeys, setDeviceKeys] = useState<any>(null);
  const [cryptoTest, setCryptoTest] = useState<{
    running: boolean;
    result: string | null;
    error: string | null;
  }>({ running: false, result: null, error: null });

  // Load available users for testing
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const { data: users, error } = await supabase
          .from('users')
          .select('id, username, display_name')
          .neq('id', user?.id)
          .eq('status', 'active')
          .limit(10);

        if (error) {
          console.error('Failed to load users:', error);
          return;
        }

        setAvailableUsers(users.map(u => ({
          id: u.id,
          username: u.username,
          displayName: u.display_name || u.username
        })));
      } catch (error) {
        console.error('Error loading users:', error);
      }
    };

    if (user) {
      loadUsers();
    }
  }, [user]);

  // Load device keys
  useEffect(() => {
    const loadDeviceKeys = async () => {
      if (device) {
        const keys = keyManagerService.getCurrentDeviceKeys();
        setDeviceKeys(keys);
      }
    };

    loadDeviceKeys();
  }, [device]);

  const runCryptoTest = async () => {
    setCryptoTest({ running: true, result: null, error: null });

    try {
      // Test key generation
      const kyberKeys = await cryptoService.generateKyberKeyPair();
      const dilithiumKeys = await cryptoService.generateDilithiumKeyPair();

      // Test encryption/decryption
      const testMessage = "Hello, this is a test of post-quantum cryptography!";
      const encryptedMessage = await cryptoService.encryptMessage(
        testMessage,
        kyberKeys.publicKey,
        dilithiumKeys.privateKey,
        dilithiumKeys.publicKey
      );

      const { message: decryptedMessage, verified } = await cryptoService.decryptMessage(
        encryptedMessage,
        kyberKeys.privateKey
      );

      const success = decryptedMessage === testMessage && verified;

      setCryptoTest({
        running: false,
        result: success ? 'Post-quantum cryptography test passed!' : 'Test failed',
        error: null
      });
    } catch (error) {
      setCryptoTest({
        running: false,
        result: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Crystal Chat - Post-Quantum Secure Messaging</h1>
        <Badge variant="outline" className="text-sm">
          <Shield className="h-4 w-4 mr-2" />
          Quantum-Resistant
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sidebar */}
        <div className="space-y-4">
          {/* User Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                User Info
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <strong>Username:</strong> {user?.username}
              </div>
              <div>
                <strong>Display Name:</strong> {user?.displayName}
              </div>
              <div>
                <strong>Device:</strong> {device?.deviceName}
              </div>
            </CardContent>
          </Card>

          {/* Crypto Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Key className="h-5 w-5 mr-2" />
                Crypto Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Device Keys:</span>
                <Badge variant={deviceKeys ? "default" : "destructive"}>
                  {deviceKeys ? "Initialized" : "Not Ready"}
                </Badge>
              </div>
              
              {deviceKeys && (
                <>
                  <div className="text-xs text-gray-600">
                    <div>Kyber Public Key: {cryptoService.arrayToBase64(deviceKeys.kyberKeyPair.publicKey).slice(0, 20)}...</div>
                    <div>Dilithium Public Key: {cryptoService.arrayToBase64(deviceKeys.dilithiumKeyPair.publicKey).slice(0, 20)}...</div>
                  </div>
                </>
              )}

              <Button
                onClick={runCryptoTest}
                disabled={cryptoTest.running}
                size="sm"
                variant="outline"
                className="w-full"
              >
                <TestTube className="h-4 w-4 mr-2" />
                {cryptoTest.running ? 'Testing...' : 'Test Crypto'}
              </Button>

              {cryptoTest.result && (
                <div className="text-sm text-green-600 bg-green-50 p-2 rounded">
                  {cryptoTest.result}
                </div>
              )}

              {cryptoTest.error && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  Error: {cryptoTest.error}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Available Users */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                Start Conversation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {availableUsers.length === 0 ? (
                <div className="text-sm text-gray-500">
                  No other users available. Create another account to test messaging.
                </div>
              ) : (
                availableUsers.map(user => (
                  <Button
                    key={user.id}
                    variant={selectedRecipient === user.id ? "default" : "outline"}
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => {
                      setSelectedRecipient(user.id);
                      setSelectedRecipientName(user.displayName);
                    }}
                  >
                    {user.displayName} (@{user.username})
                  </Button>
                ))
              )}
            </CardContent>
          </Card>
        </div>

        {/* Chat Interface */}
        <div className="lg:col-span-2">
          {selectedRecipient ? (
            <ChatInterface
              recipientId={selectedRecipient}
              title={`Chat with ${selectedRecipientName}`}
            />
          ) : (
            <Card className="h-[600px] flex items-center justify-center">
              <CardContent>
                <div className="text-center space-y-4">
                  <MessageSquare className="h-16 w-16 mx-auto text-gray-400" />
                  <h3 className="text-xl font-semibold">Select a user to start chatting</h3>
                  <p className="text-gray-600">
                    Choose a user from the sidebar to begin a secure, post-quantum encrypted conversation.
                  </p>
                  <div className="text-sm text-gray-500 space-y-1">
                    <div>🔐 Messages are encrypted with CRYSTALS-Kyber</div>
                    <div>✍️ Messages are signed with CRYSTALS-Dilithium</div>
                    <div>🔥 Messages self-destruct when read</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
