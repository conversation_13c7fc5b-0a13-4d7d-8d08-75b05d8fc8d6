import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { InteractiveCircle, CircleState } from '@/components/ui/interactive-circle';
import { LoginModal } from '@/components/modals/LoginModal';
import { MessageModal } from '@/components/modals/MessageModal';
import { ContextMenu, ProfileModal, HelpModal } from '@/components/modals/ContextMenu';
import { AdminDashboard } from '@/components/modals/AdminDashboard';
import { CryptoPerformanceMonitor } from '@/components/performance/CryptoPerformanceMonitor';
import { RealtimeIndicators } from '@/components/messaging/RealtimeIndicators';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useAuth } from '@/contexts/AuthContext';
import { useMessaging } from '@/hooks/useMessaging';
import { Message } from '@/services/messaging.service';
import { messagingService } from '@/services/messaging.service';
import { messageLifecycleService } from '@/services/messageLifecycle.service';
import { adminService } from '@/services/admin.service';

export default function CircularInterface() {
  const { user, isAuthenticated } = useAuth();
  const {
    messages,
    refreshMessages,
    typingUsers,
    onlineUsers,
    connectionState,
    messageStatuses,
    sendTypingIndicator
  } = useMessaging({ autoRefresh: true, refreshInterval: 5000 });
  
  // Modal states
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [showPerformanceModal, setShowPerformanceModal] = useState(false);
  const [showAdminDashboard, setShowAdminDashboard] = useState(false);

  // Admin state
  const [isAdmin, setIsAdmin] = useState(false);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  
  // Message handling states
  const [messageMode, setMessageMode] = useState<'send' | 'read'>('send');
  const [currentMessage, setCurrentMessage] = useState<Message | null>(null);
  const [messageQueue, setMessageQueue] = useState<Message[]>([]);
  const [isProcessingMessage, setIsProcessingMessage] = useState(false);

  // Update message queue when new messages arrive
  useEffect(() => {
    if (messages && messages.length > 0) {
      const unreadMessages = messages.filter(msg => !msg.readAt);
      setMessageQueue(unreadMessages);
    } else {
      setMessageQueue([]);
    }
  }, [messages]);

  // Check admin status when user changes
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (isAuthenticated && user) {
        const adminStatus = await adminService.isCurrentUserAdmin();
        setIsAdmin(adminStatus);
      } else {
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [isAuthenticated, user]);

  // Determine circle state based on authentication and messages
  const getCircleState = (): CircleState => {
    if (isProcessingMessage) {
      return { type: 'reading', isLoading: true };
    }
    
    if (!isAuthenticated) {
      return { type: 'guest' };
    }
    
    if (messageQueue.length > 0) {
      return { 
        type: 'new-message', 
        messageCount: messageQueue.length 
      };
    }
    
    return { type: 'authenticated' };
  };

  const handleCircleTap = () => {
    const state = getCircleState();
    
    switch (state.type) {
      case 'guest':
        setShowLoginModal(true);
        break;
        
      case 'authenticated':
        setMessageMode('send');
        setCurrentMessage(null);
        setShowMessageModal(true);
        break;
        
      case 'new-message':
        if (messageQueue.length > 0) {
          setCurrentMessage(messageQueue[0]);
          setMessageMode('read');
          setShowMessageModal(true);
        }
        break;
        
      default:
        break;
    }
  };

  const handleCircleLongPress = () => {
    if (!isAuthenticated) {
      setShowHelpModal(true);
    } else if (isAdmin) {
      setShowAdminDashboard(true);
    } else {
      setShowContextMenu(true);
    }
  };

  const handleCircleMouseDown = () => {
    if (isAuthenticated && isAdmin) {
      const timer = setTimeout(() => {
        setShowAdminDashboard(true);
      }, 1000); // 1 second long press for admin dashboard
      setLongPressTimer(timer);
    }
  };

  const handleCircleMouseUp = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  const handleSendMessage = async (content: string, recipientUsername: string) => {
    try {
      const result = await messagingService.sendMessage({
        content,
        recipientUsername,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      });
      
      if (result.success) {
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Send message error:', error);
      return { success: false, error: 'Failed to send message' };
    }
  };

  const handleMarkAsRead = async (messageId: string) => {
    try {
      setIsProcessingMessage(true);
      
      const result = await messagingService.markAsRead(messageId);
      
      if (result.success) {
        // Remove the read message from queue
        setMessageQueue(prev => prev.filter(msg => msg.id !== messageId));
        
        // Refresh messages to get updated state
        await refreshMessages();
        
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Mark as read error:', error);
      return { success: false, error: 'Failed to read message' };
    } finally {
      setIsProcessingMessage(false);
    }
  };

  const handleMessageModalClose = () => {
    setShowMessageModal(false);
    setCurrentMessage(null);
    
    // If there are more messages in queue, prepare for the next one
    if (messageMode === 'read' && messageQueue.length > 1) {
      setTimeout(() => {
        const nextMessage = messageQueue[1]; // Get next message after current one
        if (nextMessage) {
          setCurrentMessage(nextMessage);
          setShowMessageModal(true);
        }
      }, 500); // Small delay for better UX
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 via-transparent to-blue-500/10"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen w-full max-w-md mx-auto">
        {/* Real-time Indicators - Top */}
        {isAuthenticated && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="absolute top-20 left-1/2 transform -translate-x-1/2 w-full max-w-sm"
          >
            <div className="bg-slate-800/80 backdrop-blur-sm px-4 py-2 rounded-lg border border-slate-700/50">
              <RealtimeIndicators
                connectionState={connectionState}
                typingUsers={typingUsers}
                onlineUsers={onlineUsers}
                messageStatuses={messageStatuses}
                currentUserId={user?.id}
                showPresence={true}
                showTyping={true}
                showConnectionStatus={true}
              />
            </div>
          </motion.div>
        )}

        {/* Interactive Circle - Center */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            delay: 0.2
          }}
          className="relative"
        >
          {/* Glow effect */}
          <div className="absolute inset-0 rounded-full bg-red-500/20 blur-xl scale-110 animate-pulse"></div>

          <InteractiveCircle
            state={getCircleState()}
            onTap={handleCircleTap}
            onLongPress={handleCircleLongPress}
            size="xl"
            disabled={isProcessingMessage}
          />
        </motion.div>



      </div>

      {/* Bottom instruction text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.0 }}
        className="absolute bottom-6 left-1/2 transform -translate-x-1/2"
      >
        <p className="text-slate-500 text-xs text-center">
          ESC to close modals • Right-click circle for menu
          {isAdmin && <span className="block mt-1">Long-press circle for admin dashboard</span>}
        </p>
      </motion.div>

      {/* Security indicator - top corner */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.8 }}
        className="absolute top-6 right-6"
      >
        <div className="flex items-center space-x-2 bg-slate-800/60 backdrop-blur-sm px-3 py-2 rounded-full border border-slate-700/50">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-xs font-medium text-slate-300">Quantum-Safe</span>
        </div>
      </motion.div>

      {/* Modals */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
      />

      <MessageModal
        isOpen={showMessageModal}
        onClose={handleMessageModalClose}
        mode={messageMode}
        message={currentMessage}
        onSendMessage={handleSendMessage}
        onMarkAsRead={handleMarkAsRead}
      />

      <ContextMenu
        isOpen={showContextMenu}
        onClose={() => setShowContextMenu(false)}
        onProfileClick={() => setShowProfileModal(true)}
        onHelpClick={() => setShowHelpModal(true)}
        onPerformanceClick={() => setShowPerformanceModal(true)}
      />

      <ProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
      />

      <HelpModal
        isOpen={showHelpModal}
        onClose={() => setShowHelpModal(false)}
      />

      <Dialog open={showPerformanceModal} onOpenChange={setShowPerformanceModal}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Crypto Performance Monitor</DialogTitle>
          </DialogHeader>
          <CryptoPerformanceMonitor />
        </DialogContent>
      </Dialog>

      <AdminDashboard
        isOpen={showAdminDashboard}
        onClose={() => setShowAdminDashboard(false)}
      />
    </div>
  );
}
