import { UserProfile } from "@/components/auth/UserProfile";
import { useCurrentUser } from "@/contexts/AuthContext";

const Index = () => {
  const { user, isAuthenticated } = useCurrentUser();

  if (!isAuthenticated || !user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Crystal Chat</h1>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="w-full max-w-4xl space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Welcome to Crystal Chat</h1>
          <p className="text-muted-foreground">
            Quantum-secure messaging with post-quantum cryptography
          </p>
        </div>

        <div className="flex justify-center">
          <UserProfile />
        </div>

        <div className="text-center text-sm text-muted-foreground">
          <p>Authentication system is now active!</p>
          <p>Next: Implementing post-quantum cryptography and messaging features...</p>
        </div>
      </div>
    </div>
  );
};

export default Index;