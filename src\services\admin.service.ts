import { supabase } from '@/integrations/supabase/client';
import { authService } from './auth.service';

export interface AdminUser {
  id: string;
  username: string;
  displayName: string;
  status: 'active' | 'inactive' | 'suspended';
  role: string;
  lastSeen: string;
  createdAt: string;
  inviteCode?: string;
}

export interface PendingInvite {
  id: string;
  code: string;
  createdBy: string;
  creatorUsername: string;
  createdAt: string;
  expiresAt: string;
  maxUses: number;
  currentUses: number;
  metadata: any;
}

export interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  totalMessages: number;
  messagesToday: number;
  totalInvites: number;
  pendingInvites: number;
  activeDevices: number;
}

/**
 * Admin Service
 * Handles administrative functions for Crystal Chat
 */
class AdminService {
  /**
   * Check if current user is an admin
   */
  async isCurrentUserAdmin(): Promise<boolean> {
    try {
      const session = authService.getCurrentSession();
      if (!session) return false;

      const { data, error } = await supabase.rpc('is_admin', {
        user_id: session.user.id
      });

      if (error) {
        console.error('Error checking admin status:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Get all users (admin only)
   */
  async getAllUsers(): Promise<{ success: boolean; users?: AdminUser[]; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('get_all_users');

      if (error) {
        console.error('Error fetching users:', error);
        return { success: false, error: error.message };
      }

      const users: AdminUser[] = data.map((user: any) => ({
        id: user.id,
        username: user.username,
        displayName: user.display_name,
        status: user.status,
        role: user.role,
        lastSeen: user.last_seen,
        createdAt: user.created_at,
        inviteCode: user.invite_code
      }));

      return { success: true, users };
    } catch (error) {
      console.error('Error fetching users:', error);
      return { success: false, error: 'Failed to fetch users' };
    }
  }

  /**
   * Get pending invite requests (admin only)
   */
  async getPendingInvites(): Promise<{ success: boolean; invites?: PendingInvite[]; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('get_pending_invites');

      if (error) {
        console.error('Error fetching pending invites:', error);
        return { success: false, error: error.message };
      }

      const invites: PendingInvite[] = data.map((invite: any) => ({
        id: invite.id,
        code: invite.code,
        createdBy: invite.created_by,
        creatorUsername: invite.creator_username,
        createdAt: invite.created_at,
        expiresAt: invite.expires_at,
        maxUses: invite.max_uses,
        currentUses: invite.current_uses,
        metadata: invite.metadata
      }));

      return { success: true, invites };
    } catch (error) {
      console.error('Error fetching pending invites:', error);
      return { success: false, error: 'Failed to fetch pending invites' };
    }
  }

  /**
   * Approve or reject an invite request (admin only)
   */
  async manageInviteRequest(
    inviteId: string,
    action: 'approve' | 'reject',
    adminNotes?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('manage_invite_request', {
        invite_id: inviteId,
        action,
        admin_notes: adminNotes
      });

      if (error) {
        console.error('Error managing invite request:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error managing invite request:', error);
      return { success: false, error: 'Failed to manage invite request' };
    }
  }

  /**
   * Get system statistics (admin only)
   */
  async getSystemStats(): Promise<{ success: boolean; stats?: SystemStats; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('get_system_stats');

      if (error) {
        console.error('Error fetching system stats:', error);
        return { success: false, error: error.message };
      }

      if (!data || data.length === 0) {
        return { success: false, error: 'No statistics available' };
      }

      const stats: SystemStats = {
        totalUsers: data[0].total_users,
        activeUsers: data[0].active_users,
        totalMessages: data[0].total_messages,
        messagesToday: data[0].messages_today,
        totalInvites: data[0].total_invites,
        pendingInvites: data[0].pending_invites,
        activeDevices: data[0].active_devices
      };

      return { success: true, stats };
    } catch (error) {
      console.error('Error fetching system stats:', error);
      return { success: false, error: 'Failed to fetch system statistics' };
    }
  }

  /**
   * Update user status (admin only)
   */
  async updateUserStatus(
    userId: string,
    newStatus: 'active' | 'inactive' | 'suspended',
    adminNotes?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('admin_update_user_status', {
        target_user_id: userId,
        new_status: newStatus,
        admin_notes: adminNotes
      });

      if (error) {
        console.error('Error updating user status:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating user status:', error);
      return { success: false, error: 'Failed to update user status' };
    }
  }

  /**
   * Create a new invite code (admin only)
   */
  async createInviteCode(options: {
    maxUses?: number;
    expiresInDays?: number;
    notes?: string;
  } = {}): Promise<{ success: boolean; code?: string; error?: string }> {
    try {
      const session = authService.getCurrentSession();
      if (!session) {
        return { success: false, error: 'Not authenticated' };
      }

      const {
        maxUses = 1,
        expiresInDays = 7,
        notes
      } = options;

      const { data, error } = await supabase.rpc('generate_invite_code', {
        p_created_by: session.user.id,
        p_max_uses: maxUses,
        p_expires_in_days: expiresInDays,
        p_notes: notes
      });

      if (error) {
        console.error('Error creating invite code:', error);
        return { success: false, error: error.message };
      }

      return { success: true, code: data };
    } catch (error) {
      console.error('Error creating invite code:', error);
      return { success: false, error: 'Failed to create invite code' };
    }
  }
}

export const adminService = new AdminService();
