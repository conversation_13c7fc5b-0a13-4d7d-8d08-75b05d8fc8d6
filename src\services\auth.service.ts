import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';
import { keyManagerService } from './keyManager.service';

export type User = Database['public']['Tables']['users']['Row'];
export type Device = Database['public']['Tables']['devices']['Row'];

export interface AuthUser {
  id: string;
  username: string;
  displayName: string;
  status: string;
  lastSeen: string;
}

export interface AuthDevice {
  id: string;
  deviceName: string;
  deviceFingerprint: string;
  status: string;
  lastUsed: string;
}

export interface AuthSession {
  user: AuthUser;
  device: AuthDevice;
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

class AuthService {
  private currentSession: AuthSession | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  /**
   * Create a new user account with secret word authentication
   */
  async createUser(
    username: string, 
    secretWord: string, 
    displayName?: string
  ): Promise<{ success: boolean; userId?: string; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('create_user_with_secret', {
        p_username: username,
        p_secret_word: secretWord,
        p_display_name: displayName || username
      });

      if (error) {
        console.error('User creation error:', error);
        return { success: false, error: error.message };
      }

      return { success: true, userId: data };
    } catch (error) {
      console.error('User creation failed:', error);
      return { success: false, error: 'Failed to create user account' };
    }
  }

  /**
   * Authenticate user with username and secret word
   */
  async authenticate(
    username: string, 
    secretWord: string
  ): Promise<{ success: boolean; session?: AuthSession; error?: string }> {
    try {
      // First authenticate with the database function
      const { data: authResult, error: authError } = await supabase.rpc('authenticate_user', {
        p_username: username,
        p_secret_word: secretWord
      });

      if (authError || !authResult || authResult.length === 0 || !authResult[0].success) {
        return { success: false, error: 'Invalid username or secret word' };
      }

      const userId = authResult[0].user_id;

      // Get user details
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (userError || !userData) {
        return { success: false, error: 'Failed to retrieve user data' };
      }

      // Create Supabase auth session
      const { data: sessionData, error: sessionError } = await supabase.auth.signInAnonymously();

      if (sessionError || !sessionData.session) {
        return { success: false, error: 'Failed to create session' };
      }

      // Get or create device for this session
      const deviceFingerprint = await this.generateDeviceFingerprint();
      const device = await this.getOrCreateDevice(userId, deviceFingerprint);

      if (!device) {
        return { success: false, error: 'Failed to register device' };
      }

      // Initialize device keys for post-quantum cryptography
      await keyManagerService.initializeDeviceKeys(device.id);

      // Create auth session - handle both schema formats
      const session: AuthSession = {
        user: {
          id: userData.id,
          username: userData.username,
          displayName: userData.display_name || userData.displayName || userData.username,
          status: userData.status || (userData.isActive ? 'active' : 'inactive') || 'active',
          lastSeen: userData.last_seen || userData.lastLoginAt || new Date().toISOString()
        },
        device: {
          id: device.id,
          deviceName: device.device_name,
          deviceFingerprint: device.device_fingerprint,
          status: device.status || 'active',
          lastUsed: device.last_used || new Date().toISOString()
        },
        accessToken: sessionData.session.access_token,
        refreshToken: sessionData.session.refresh_token || '',
        expiresAt: sessionData.session.expires_at || Date.now() + 3600000
      };

      this.currentSession = session;
      this.startRefreshTimer();
      this.saveSessionToStorage(session);

      return { success: true, session };
    } catch (error) {
      console.error('Authentication failed:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  /**
   * Get current authenticated session
   */
  getCurrentSession(): AuthSession | null {
    if (!this.currentSession) {
      this.currentSession = this.loadSessionFromStorage();
    }
    return this.currentSession;
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    const session = this.getCurrentSession();
    return session !== null && session.expiresAt > Date.now();
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    try {
      await supabase.auth.signOut();
      this.clearSession();

      // Clear cryptographic keys
      keyManagerService.clearStoredKeys();
    } catch (error) {
      console.error('Sign out error:', error);
      this.clearSession();
      keyManagerService.clearStoredKeys();
    }
  }

  /**
   * Refresh the current session
   */
  async refreshSession(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error || !data.session) {
        this.clearSession();
        return false;
      }

      if (this.currentSession) {
        this.currentSession.accessToken = data.session.access_token;
        this.currentSession.refreshToken = data.session.refresh_token || '';
        this.currentSession.expiresAt = data.session.expires_at || Date.now() + 3600000;
        this.saveSessionToStorage(this.currentSession);
      }

      return true;
    } catch (error) {
      console.error('Session refresh failed:', error);
      this.clearSession();
      return false;
    }
  }

  /**
   * Generate a unique device fingerprint
   */
  private async generateDeviceFingerprint(): Promise<string> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx?.fillText('Device fingerprint', 10, 10);
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
      navigator.hardwareConcurrency || 0,
      navigator.deviceMemory || 0
    ].join('|');

    // Create a hash of the fingerprint
    const encoder = new TextEncoder();
    const data = encoder.encode(fingerprint);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Get or create a device for the current session
   */
  private async getOrCreateDevice(userId: string, fingerprint: string): Promise<Device | null> {
    try {
      // First try to find existing device
      const { data: existingDevice } = await supabase
        .from('devices')
        .select('*')
        .eq('user_id', userId)
        .eq('device_fingerprint', fingerprint)
        .eq('status', 'active')
        .single();

      if (existingDevice) {
        // Update last used timestamp
        await supabase
          .from('devices')
          .update({ last_used: new Date().toISOString() })
          .eq('id', existingDevice.id);
        
        return existingDevice;
      }

      // Create new device (placeholder keys for now)
      const { data: newDevice, error } = await supabase.rpc('register_device', {
        p_user_id: userId,
        p_device_name: this.getDeviceName(),
        p_device_fingerprint: fingerprint,
        p_public_key_dilithium: 'placeholder_dilithium_key', // Will be replaced with real PQC keys
        p_public_key_kyber: 'placeholder_kyber_key' // Will be replaced with real PQC keys
      });

      if (error) {
        console.error('Device registration error:', error);
        return null;
      }

      // Fetch the created device
      const { data: deviceData } = await supabase
        .from('devices')
        .select('*')
        .eq('id', newDevice)
        .single();

      return deviceData;
    } catch (error) {
      console.error('Device creation/retrieval failed:', error);
      return null;
    }
  }

  /**
   * Get a human-readable device name
   */
  private getDeviceName(): string {
    const ua = navigator.userAgent;
    
    if (ua.includes('Chrome')) return 'Chrome Browser';
    if (ua.includes('Firefox')) return 'Firefox Browser';
    if (ua.includes('Safari')) return 'Safari Browser';
    if (ua.includes('Edge')) return 'Edge Browser';
    
    return 'Unknown Device';
  }

  /**
   * Start automatic session refresh timer
   */
  private startRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    // Refresh 5 minutes before expiration
    const refreshTime = (this.currentSession?.expiresAt || 0) - Date.now() - 300000;
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(() => {
        this.refreshSession();
      }, refreshTime);
    }
  }

  /**
   * Save session to local storage
   */
  private saveSessionToStorage(session: AuthSession): void {
    try {
      localStorage.setItem('crystal_chat_session', JSON.stringify(session));
    } catch (error) {
      console.error('Failed to save session to storage:', error);
    }
  }

  /**
   * Load session from local storage
   */
  private loadSessionFromStorage(): AuthSession | null {
    try {
      const stored = localStorage.getItem('crystal_chat_session');
      if (stored) {
        const session = JSON.parse(stored) as AuthSession;
        if (session.expiresAt > Date.now()) {
          this.startRefreshTimer();
          return session;
        }
      }
    } catch (error) {
      console.error('Failed to load session from storage:', error);
    }
    return null;
  }

  /**
   * Clear current session
   */
  private clearSession(): void {
    this.currentSession = null;
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    localStorage.removeItem('crystal_chat_session');
  }
}

export const authService = new AuthService();
