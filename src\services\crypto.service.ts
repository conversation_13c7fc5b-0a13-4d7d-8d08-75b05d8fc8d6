import { MlKem768 } from 'mlkem';
import { dilithium } from 'dilithium-crystals';
import { wasmCryptoService, WASMKeyPair, WASMEncryptedMessage, WASMDecryptedMessage } from './wasmCrypto.service';

export interface KeyPair {
  publicKey: Uint8Array;
  privateKey: Uint8Array;
}

export interface EncryptionResult {
  ciphertext: Uint8Array;
  sharedSecret: Uint8Array;
}

export interface SignatureResult {
  signature: Uint8Array;
  message: Uint8Array;
}

export interface EncryptedMessage {
  encryptedContent: Uint8Array;
  signature: Uint8Array;
  senderPublicKey: Uint8Array;
  kyberCiphertext: Uint8Array;
  nonce: Uint8Array;
}

/**
 * Post-Quantum Cryptography Service
 * Implements CRYSTALS-Kyber for encryption and CRYSTALS-Dilithium for signatures
 */
class CryptoService {
  private kyberInstance: MlKem768;
  private useWASMOptimization = false; // Disabled due to external dependency issues
  private performanceMode: 'balanced' | 'speed' | 'security' = 'security';
  private isWASMInitialized = false;

  constructor() {
    this.kyberInstance = new MlKem768();
    this.initializeWASM();
  }

  /**
   * Initialize WASM modules for hardware acceleration
   * Disabled due to external dependency issues
   */
  private async initializeWASM(): Promise<void> {
    console.warn('WASM optimization disabled due to external dependency issues, using JS fallback');
    this.useWASMOptimization = false;
    this.performanceMode = 'security';
    this.isWASMInitialized = false;
  }

  /**
   * Generate a new Kyber key pair for encryption with WASM optimization
   */
  async generateKyberKeyPair(): Promise<KeyPair> {
    try {
      // Use WASM optimization if available and performance mode allows
      if (this.useWASMOptimization && this.isWASMInitialized && this.performanceMode !== 'security') {
        const wasmKeyPair = await wasmCryptoService.generateKyberKeyPair();
        return {
          publicKey: wasmKeyPair.publicKey,
          privateKey: wasmKeyPair.privateKey
        };
      }

      // Fallback to JS implementation
      const [publicKey, privateKey] = await this.kyberInstance.generateKeyPair();
      return {
        publicKey,
        privateKey
      };
    } catch (error) {
      console.error('Failed to generate Kyber key pair:', error);
      throw new Error('Key generation failed');
    }
  }

  /**
   * Generate a new Dilithium key pair for signatures with WASM optimization
   */
  async generateDilithiumKeyPair(): Promise<KeyPair> {
    try {
      // Use WASM optimization if available and performance mode allows
      if (this.useWASMOptimization && this.isWASMInitialized && this.performanceMode !== 'security') {
        const wasmKeyPair = await wasmCryptoService.generateDilithiumKeyPair();
        return {
          publicKey: wasmKeyPair.publicKey,
          privateKey: wasmKeyPair.privateKey
        };
      }

      // Fallback to JS implementation
      const keyPair = await dilithium.keyPair();
      return {
        publicKey: keyPair.publicKey,
        privateKey: keyPair.privateKey
      };
    } catch (error) {
      console.error('Failed to generate Dilithium key pair:', error);
      throw new Error('Signature key generation failed');
    }
  }

  /**
   * Encapsulate a shared secret using Kyber public key
   */
  async encapsulate(publicKey: Uint8Array): Promise<EncryptionResult> {
    try {
      const [ciphertext, sharedSecret] = await this.kyberInstance.encap(publicKey);
      return {
        ciphertext,
        sharedSecret
      };
    } catch (error) {
      console.error('Failed to encapsulate:', error);
      throw new Error('Encapsulation failed');
    }
  }

  /**
   * Decapsulate a shared secret using Kyber private key
   */
  async decapsulate(ciphertext: Uint8Array, privateKey: Uint8Array): Promise<Uint8Array> {
    try {
      return await this.kyberInstance.decap(ciphertext, privateKey);
    } catch (error) {
      console.error('Failed to decapsulate:', error);
      throw new Error('Decapsulation failed');
    }
  }

  /**
   * Sign a message using Dilithium private key
   */
  async sign(message: Uint8Array, privateKey: Uint8Array): Promise<Uint8Array> {
    try {
      return await dilithium.signDetached(message, privateKey);
    } catch (error) {
      console.error('Failed to sign message:', error);
      throw new Error('Message signing failed');
    }
  }

  /**
   * Verify a signature using Dilithium public key
   */
  async verify(signature: Uint8Array, message: Uint8Array, publicKey: Uint8Array): Promise<boolean> {
    try {
      return await dilithium.verifyDetached(signature, message, publicKey);
    } catch (error) {
      console.error('Failed to verify signature:', error);
      return false;
    }
  }

  /**
   * Encrypt a message using hybrid post-quantum cryptography
   * Uses Kyber for key encapsulation and AES-GCM for symmetric encryption
   */
  async encryptMessage(
    message: string,
    recipientKyberPublicKey: Uint8Array,
    senderDilithiumPrivateKey: Uint8Array,
    senderDilithiumPublicKey: Uint8Array
  ): Promise<EncryptedMessage> {
    try {
      // Convert message to bytes
      const messageBytes = new TextEncoder().encode(message);

      // Generate shared secret using Kyber
      const { ciphertext: kyberCiphertext, sharedSecret } = await this.encapsulate(recipientKyberPublicKey);

      // Generate a random nonce for AES-GCM
      const nonce = crypto.getRandomValues(new Uint8Array(12));

      // Derive AES key from shared secret
      const aesKey = await crypto.subtle.importKey(
        'raw',
        sharedSecret.slice(0, 32), // Use first 32 bytes as AES-256 key
        { name: 'AES-GCM' },
        false,
        ['encrypt']
      );

      // Encrypt the message with AES-GCM
      const encryptedContent = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: nonce
        },
        aesKey,
        messageBytes
      );

      // Create signature payload (encrypted content + nonce + kyber ciphertext)
      const signaturePayload = new Uint8Array(
        encryptedContent.byteLength + nonce.length + kyberCiphertext.length
      );
      signaturePayload.set(new Uint8Array(encryptedContent), 0);
      signaturePayload.set(nonce, encryptedContent.byteLength);
      signaturePayload.set(kyberCiphertext, encryptedContent.byteLength + nonce.length);

      // Sign the payload with Dilithium
      const signature = await this.sign(signaturePayload, senderDilithiumPrivateKey);

      return {
        encryptedContent: new Uint8Array(encryptedContent),
        signature,
        senderPublicKey: senderDilithiumPublicKey,
        kyberCiphertext,
        nonce
      };
    } catch (error) {
      console.error('Failed to encrypt message:', error);
      throw new Error('Message encryption failed');
    }
  }

  /**
   * Decrypt a message using hybrid post-quantum cryptography
   */
  async decryptMessage(
    encryptedMessage: EncryptedMessage,
    recipientKyberPrivateKey: Uint8Array
  ): Promise<{ message: string; verified: boolean }> {
    try {
      // Verify signature first
      const signaturePayload = new Uint8Array(
        encryptedMessage.encryptedContent.length + 
        encryptedMessage.nonce.length + 
        encryptedMessage.kyberCiphertext.length
      );
      signaturePayload.set(encryptedMessage.encryptedContent, 0);
      signaturePayload.set(encryptedMessage.nonce, encryptedMessage.encryptedContent.length);
      signaturePayload.set(
        encryptedMessage.kyberCiphertext, 
        encryptedMessage.encryptedContent.length + encryptedMessage.nonce.length
      );

      const verified = await this.verify(
        encryptedMessage.signature,
        signaturePayload,
        encryptedMessage.senderPublicKey
      );

      // Decapsulate shared secret using Kyber
      const sharedSecret = await this.decapsulate(
        encryptedMessage.kyberCiphertext,
        recipientKyberPrivateKey
      );

      // Derive AES key from shared secret
      const aesKey = await crypto.subtle.importKey(
        'raw',
        sharedSecret.slice(0, 32), // Use first 32 bytes as AES-256 key
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );

      // Decrypt the message with AES-GCM
      const decryptedBytes = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: encryptedMessage.nonce
        },
        aesKey,
        encryptedMessage.encryptedContent
      );

      // Convert back to string
      const message = new TextDecoder().decode(decryptedBytes);

      return { message, verified };
    } catch (error) {
      console.error('Failed to decrypt message:', error);
      throw new Error('Message decryption failed');
    }
  }

  /**
   * Convert Uint8Array to base64 string for storage/transmission
   */
  arrayToBase64(array: Uint8Array): string {
    return btoa(String.fromCharCode(...array));
  }

  /**
   * Convert base64 string back to Uint8Array
   */
  base64ToArray(base64: string): Uint8Array {
    return new Uint8Array(atob(base64).split('').map(char => char.charCodeAt(0)));
  }

  /**
   * Generate device-specific key pairs for a user
   */
  async generateDeviceKeys(): Promise<{
    kyberKeyPair: KeyPair;
    dilithiumKeyPair: KeyPair;
  }> {
    try {
      const [kyberKeyPair, dilithiumKeyPair] = await Promise.all([
        this.generateKyberKeyPair(),
        this.generateDilithiumKeyPair()
      ]);

      return {
        kyberKeyPair,
        dilithiumKeyPair
      };
    } catch (error) {
      console.error('Failed to generate device keys:', error);
      throw new Error('Device key generation failed');
    }
  }

  /**
   * Create encryption metadata for database storage
   */
  createEncryptionMetadata(encryptedMessage: EncryptedMessage): object {
    return {
      algorithm: 'CRYSTALS-Kyber-768 + CRYSTALS-Dilithium + AES-256-GCM',
      kyberCiphertext: this.arrayToBase64(encryptedMessage.kyberCiphertext),
      nonce: this.arrayToBase64(encryptedMessage.nonce),
      senderPublicKey: this.arrayToBase64(encryptedMessage.senderPublicKey),
      timestamp: Date.now()
    };
  }

  /**
   * Parse encryption metadata from database
   */
  parseEncryptionMetadata(metadata: any): {
    kyberCiphertext: Uint8Array;
    nonce: Uint8Array;
    senderPublicKey: Uint8Array;
  } {
    return {
      kyberCiphertext: this.base64ToArray(metadata.kyberCiphertext),
      nonce: this.base64ToArray(metadata.nonce),
      senderPublicKey: this.base64ToArray(metadata.senderPublicKey)
    };
  }

  /**
   * Get performance metrics from WASM and JS implementations
   */
  getPerformanceMetrics(): {
    wasm: any;
    mode: string;
    optimization: boolean;
    hardware: any;
  } {
    return {
      wasm: this.isWASMInitialized ? wasmCryptoService.getPerformanceMetrics() : null,
      mode: this.performanceMode,
      optimization: this.useWASMOptimization,
      hardware: this.isWASMInitialized ? wasmCryptoService.getHardwareCapabilities() : null
    };
  }

  /**
   * Set performance mode
   */
  setPerformanceMode(mode: 'balanced' | 'speed' | 'security'): void {
    this.performanceMode = mode;

    // Disable WASM optimization in security mode
    if (mode === 'security') {
      this.useWASMOptimization = false;
    } else if (this.isWASMInitialized) {
      this.useWASMOptimization = true;
    }
  }

  /**
   * Reset performance metrics
   */
  resetPerformanceMetrics(): void {
    if (this.isWASMInitialized) {
      wasmCryptoService.resetPerformanceMetrics();
    }
  }

  /**
   * Benchmark cryptographic operations
   */
  async benchmarkOperations(): Promise<{
    keyGeneration: { wasm: number; js: number };
    encryption: { wasm: number; js: number };
    signing: { wasm: number; js: number };
  }> {
    const results = {
      keyGeneration: { wasm: 0, js: 0 },
      encryption: { wasm: 0, js: 0 },
      signing: { wasm: 0, js: 0 }
    };

    // Benchmark key generation
    if (this.isWASMInitialized) {
      const wasmStart = performance.now();
      await wasmCryptoService.generateKyberKeyPair();
      await wasmCryptoService.generateDilithiumKeyPair();
      results.keyGeneration.wasm = performance.now() - wasmStart;
    }

    const jsStart = performance.now();
    await this.kyberInstance.generateKeyPair();
    await dilithium.generateKeyPair();
    results.keyGeneration.js = performance.now() - jsStart;

    return results;
  }

  /**
   * Cleanup WASM resources
   */
  cleanup(): void {
    if (this.isWASMInitialized) {
      wasmCryptoService.cleanup();
    }
  }
}

export const cryptoService = new CryptoService();
