import { supabase } from '@/integrations/supabase/client';

export interface InviteCode {
  id: string;
  code: string;
  createdBy: string;
  requestedBy: string;
  maxUses: number;
  currentUses: number;
  expiresAt: string;
  status: 'pending' | 'approved' | 'rejected' | 'used';
  approvedBy?: string;
  approvedAt?: string;
  requestedAt: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InviteValidationResult {
  valid: boolean;
  message: string;
  inviteId?: string;
  maxUses?: number;
  currentUses?: number;
  expiresAt?: string;
}

export interface PendingInvite {
  code: string;
  requestedByUsername: string;
  requestedByEmail: string;
  requestedAt: string;
  maxUses: number;
  expiresAt: string;
  notes?: string;
}

export interface CreateInviteOptions {
  maxUses?: number;
  expiresInDays?: number;
  notes?: string;
}

class InviteService {
  /**
   * Request a new QSC-XXX invite code (requires admin approval)
   */
  async requestInviteCode(
    requestedBy: string,
    options: CreateInviteOptions = {}
  ): Promise<{ success: boolean; code?: string; error?: string }> {
    try {
      const {
        maxUses = 1,
        expiresInDays = 7,
        notes
      } = options;

      const { data, error } = await supabase.rpc('generate_invite_code', {
        p_requested_by: requestedBy,
        p_max_uses: maxUses,
        p_expires_in_days: expiresInDays,
        p_notes: notes
      });

      if (error) {
        console.error('Error requesting invite code:', error);
        return { success: false, error: error.message };
      }

      return {
        success: true,
        code: data,
        error: 'Invite code requested successfully. Awaiting admin approval.'
      };
    } catch (error) {
      console.error('Error requesting invite code:', error);
      return { success: false, error: 'Failed to request invite code' };
    }
  }

  /**
   * Validate an invite code using the database function
   */
  async validateInviteCode(code: string): Promise<InviteValidationResult> {
    try {
      const { data, error } = await supabase.rpc('validate_invite_code', {
        p_code: code.toUpperCase()
      });

      if (error || !data || data.length === 0) {
        return {
          valid: false,
          message: 'Failed to validate invite code'
        };
      }

      const result = data[0];
      return {
        valid: result.valid,
        message: result.message,
        inviteId: result.invite_id,
        maxUses: result.max_uses,
        currentUses: result.current_uses,
        expiresAt: result.expires_at
      };
    } catch (error) {
      console.error('Error validating invite code:', error);
      return {
        valid: false,
        message: 'Failed to validate invite code'
      };
    }
  }

  /**
   * Use an invite code (increment usage count)
   */
  async useInviteCode(
    code: string,
    usedBy: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // First validate the invite
      const validation = await this.validateInviteCode(code);
      if (!validation.valid || !validation.inviteId) {
        return { success: false, error: validation.message };
      }

      // Update the invite usage count
      const { error } = await supabase
        .from('invite_codes')
        .update({
          current_uses: (validation.currentUses || 0) + 1,
          updatedAt: new Date().toISOString()
        })
        .eq('id', validation.inviteId);

      if (error) {
        console.error('Error using invite code:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error using invite code:', error);
      return { success: false, error: 'Failed to use invite code' };
    }
  }

  /**
   * Get pending invite codes for admin approval
   */
  async getPendingInvites(): Promise<{ success: boolean; invites?: PendingInvite[]; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('get_pending_invites');

      if (error) {
        console.error('Error fetching pending invites:', error);
        return { success: false, error: error.message };
      }

      const invites: PendingInvite[] = (data || []).map((item: any) => ({
        code: item.code,
        requestedByUsername: item.requested_by_username,
        requestedByEmail: item.requested_by_email,
        requestedAt: item.requested_at,
        maxUses: item.max_uses,
        expiresAt: item.expires_at,
        notes: item.notes
      }));

      return { success: true, invites };
    } catch (error) {
      console.error('Error fetching pending invites:', error);
      return { success: false, error: 'Failed to fetch pending invites' };
    }
  }

  /**
   * Approve or reject an invite code (admin function)
   */
  async approveInviteCode(
    code: string,
    adminId: string,
    approved: boolean,
    notes?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('approve_invite_code', {
        p_code: code,
        p_admin_id: adminId,
        p_approved: approved,
        p_notes: notes
      });

      if (error) {
        console.error('Error approving invite code:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error approving invite code:', error);
      return { success: false, error: 'Failed to approve invite code' };
    }
  }

  /**
   * Check if user can request invites (all authenticated users can)
   */
  async canRequestInvites(userId: string): Promise<boolean> {
    try {
      // All authenticated users can request invites
      const { data: user } = await supabase.auth.getUser();
      return !!user?.user;
    } catch (error) {
      console.error('Error checking invite permissions:', error);
      return false;
    }
  }

  /**
   * Get user's invite code requests
   */
  async getUserInviteRequests(userId: string): Promise<{
    success: boolean;
    invites?: InviteCode[];
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('invite_codes')
        .select('*')
        .eq('requested_by', userId)
        .order('requested_at', { ascending: false });

      if (error) {
        console.error('Error fetching user invite requests:', error);
        return { success: false, error: error.message };
      }

      const invites: InviteCode[] = (data || []).map((item: any) => ({
        id: item.id,
        code: item.code,
        createdBy: item.created_by,
        requestedBy: item.requested_by,
        maxUses: item.max_uses,
        currentUses: item.current_uses,
        expiresAt: item.expires_at,
        status: item.status,
        approvedBy: item.approved_by,
        approvedAt: item.approved_at,
        requestedAt: item.requested_at,
        notes: item.notes,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));

      return { success: true, invites };
    } catch (error) {
      console.error('Error fetching user invite requests:', error);
      return { success: false, error: 'Failed to fetch invite requests' };
    }
  }
}

export const inviteService = new InviteService();
