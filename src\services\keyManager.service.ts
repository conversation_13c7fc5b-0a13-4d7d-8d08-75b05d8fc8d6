import { cryptoService, KeyPair } from './crypto.service';
import { supabase } from '@/integrations/supabase/client';

export interface DeviceKeys {
  kyberKeyPair: KeyPair;
  dilithiumKeyPair: KeyPair;
  deviceId: string;
}

export interface StoredKeys {
  kyberPrivateKey: string;
  dilithiumPrivateKey: string;
  deviceId: string;
}

/**
 * Key Manager Service
 * Handles generation, storage, and retrieval of post-quantum cryptographic keys
 */
class KeyManagerService {
  private readonly STORAGE_KEY = 'crystal_chat_device_keys';
  private currentDeviceKeys: DeviceKeys | null = null;

  /**
   * Initialize or retrieve device keys for the current device
   */
  async initializeDeviceKeys(deviceId: string): Promise<DeviceKeys> {
    try {
      // Try to load existing keys from secure storage
      let deviceKeys = await this.loadDeviceKeys(deviceId);

      if (!deviceKeys) {
        // Generate new keys if none exist
        deviceKeys = await this.generateAndStoreDeviceKeys(deviceId);
      }

      this.currentDeviceKeys = deviceKeys;
      return deviceKeys;
    } catch (error) {
      console.error('Failed to initialize device keys:', error);
      throw new Error('Device key initialization failed');
    }
  }

  /**
   * Generate new device keys and store them securely
   */
  async generateAndStoreDeviceKeys(deviceId: string): Promise<DeviceKeys> {
    try {
      // Generate new key pairs
      const { kyberKeyPair, dilithiumKeyPair } = await cryptoService.generateDeviceKeys();

      const deviceKeys: DeviceKeys = {
        kyberKeyPair,
        dilithiumKeyPair,
        deviceId
      };

      // Store keys securely
      await this.storeDeviceKeys(deviceKeys);

      // Update device record in database with public keys
      await this.updateDevicePublicKeys(deviceId, kyberKeyPair.publicKey, dilithiumKeyPair.publicKey);

      return deviceKeys;
    } catch (error) {
      console.error('Failed to generate and store device keys:', error);
      throw new Error('Device key generation failed');
    }
  }

  /**
   * Store device keys securely in browser storage
   */
  private async storeDeviceKeys(deviceKeys: DeviceKeys): Promise<void> {
    try {
      const storedKeys: StoredKeys = {
        kyberPrivateKey: cryptoService.arrayToBase64(deviceKeys.kyberKeyPair.privateKey),
        dilithiumPrivateKey: cryptoService.arrayToBase64(deviceKeys.dilithiumKeyPair.privateKey),
        deviceId: deviceKeys.deviceId
      };

      // Store in localStorage (in production, consider using IndexedDB or WebCrypto for better security)
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(storedKeys));
    } catch (error) {
      console.error('Failed to store device keys:', error);
      throw new Error('Key storage failed');
    }
  }

  /**
   * Load device keys from secure storage
   */
  private async loadDeviceKeys(deviceId: string): Promise<DeviceKeys | null> {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) {
        return null;
      }

      const storedKeys: StoredKeys = JSON.parse(stored);
      
      // Verify this is for the correct device
      if (storedKeys.deviceId !== deviceId) {
        return null;
      }

      // Get public keys from database
      const { data: deviceData, error } = await supabase
        .from('devices')
        .select('public_key_kyber, public_key_dilithium')
        .eq('id', deviceId)
        .single();

      if (error || !deviceData) {
        console.error('Failed to load device public keys:', error);
        return null;
      }

      return {
        kyberKeyPair: {
          privateKey: cryptoService.base64ToArray(storedKeys.kyberPrivateKey),
          publicKey: cryptoService.base64ToArray(deviceData.public_key_kyber)
        },
        dilithiumKeyPair: {
          privateKey: cryptoService.base64ToArray(storedKeys.dilithiumPrivateKey),
          publicKey: cryptoService.base64ToArray(deviceData.public_key_dilithium)
        },
        deviceId
      };
    } catch (error) {
      console.error('Failed to load device keys:', error);
      return null;
    }
  }

  /**
   * Update device record with public keys
   */
  private async updateDevicePublicKeys(
    deviceId: string, 
    kyberPublicKey: Uint8Array, 
    dilithiumPublicKey: Uint8Array
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('devices')
        .update({
          public_key_kyber: cryptoService.arrayToBase64(kyberPublicKey),
          public_key_dilithium: cryptoService.arrayToBase64(dilithiumPublicKey)
        })
        .eq('id', deviceId);

      if (error) {
        console.error('Failed to update device public keys:', error);
        throw new Error('Database update failed');
      }
    } catch (error) {
      console.error('Failed to update device public keys:', error);
      throw new Error('Device key update failed');
    }
  }

  /**
   * Get current device keys
   */
  getCurrentDeviceKeys(): DeviceKeys | null {
    return this.currentDeviceKeys;
  }

  /**
   * Get public keys for a specific device
   */
  async getDevicePublicKeys(deviceId: string): Promise<{
    kyberPublicKey: Uint8Array;
    dilithiumPublicKey: Uint8Array;
  } | null> {
    try {
      const { data: deviceData, error } = await supabase
        .from('devices')
        .select('public_key_kyber, public_key_dilithium')
        .eq('id', deviceId)
        .eq('status', 'active')
        .single();

      if (error || !deviceData) {
        console.error('Failed to get device public keys:', error);
        return null;
      }

      return {
        kyberPublicKey: cryptoService.base64ToArray(deviceData.public_key_kyber),
        dilithiumPublicKey: cryptoService.base64ToArray(deviceData.public_key_dilithium)
      };
    } catch (error) {
      console.error('Failed to get device public keys:', error);
      return null;
    }
  }

  /**
   * Get public keys for a user (all their devices)
   */
  async getUserDeviceKeys(userId: string): Promise<Array<{
    deviceId: string;
    deviceName: string;
    kyberPublicKey: Uint8Array;
    dilithiumPublicKey: Uint8Array;
  }>> {
    try {
      const { data: devices, error } = await supabase
        .from('devices')
        .select('id, device_name, public_key_kyber, public_key_dilithium')
        .eq('user_id', userId)
        .eq('status', 'active');

      if (error) {
        console.error('Failed to get user device keys:', error);
        return [];
      }

      return devices.map(device => ({
        deviceId: device.id,
        deviceName: device.device_name,
        kyberPublicKey: cryptoService.base64ToArray(device.public_key_kyber),
        dilithiumPublicKey: cryptoService.base64ToArray(device.public_key_dilithium)
      }));
    } catch (error) {
      console.error('Failed to get user device keys:', error);
      return [];
    }
  }

  /**
   * Revoke device keys (mark device as inactive)
   */
  async revokeDeviceKeys(deviceId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('devices')
        .update({ status: 'revoked' })
        .eq('id', deviceId);

      if (error) {
        console.error('Failed to revoke device keys:', error);
        throw new Error('Device revocation failed');
      }

      // Clear local storage if this is the current device
      if (this.currentDeviceKeys?.deviceId === deviceId) {
        localStorage.removeItem(this.STORAGE_KEY);
        this.currentDeviceKeys = null;
      }
    } catch (error) {
      console.error('Failed to revoke device keys:', error);
      throw new Error('Device revocation failed');
    }
  }

  /**
   * Clear all stored keys (for logout)
   */
  clearStoredKeys(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    this.currentDeviceKeys = null;
  }

  /**
   * Validate key integrity
   */
  async validateKeys(deviceKeys: DeviceKeys): Promise<boolean> {
    try {
      // Test encryption/decryption with Kyber keys
      const testMessage = new Uint8Array([1, 2, 3, 4, 5]);
      const { ciphertext, sharedSecret } = await cryptoService.encapsulate(deviceKeys.kyberKeyPair.publicKey);
      const decryptedSecret = await cryptoService.decapsulate(ciphertext, deviceKeys.kyberKeyPair.privateKey);
      
      // Check if shared secrets match
      if (sharedSecret.length !== decryptedSecret.length) {
        return false;
      }
      
      for (let i = 0; i < sharedSecret.length; i++) {
        if (sharedSecret[i] !== decryptedSecret[i]) {
          return false;
        }
      }

      // Test signing/verification with Dilithium keys
      const signature = await cryptoService.sign(testMessage, deviceKeys.dilithiumKeyPair.privateKey);
      const verified = await cryptoService.verify(signature, testMessage, deviceKeys.dilithiumKeyPair.publicKey);

      return verified;
    } catch (error) {
      console.error('Key validation failed:', error);
      return false;
    }
  }
}

export const keyManagerService = new KeyManagerService();
