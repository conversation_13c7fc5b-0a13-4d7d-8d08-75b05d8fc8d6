import { supabase } from '@/integrations/supabase/client';
import { authService } from './auth.service';

export interface MessageLifecycleConfig {
  defaultExpirationMinutes: number;
  maxExpirationMinutes: number;
  cleanupIntervalMinutes: number;
  autoDestroyOnRead: boolean;
  secureDeletePasses: number;
}

export interface MessageExpirationSettings {
  messageId: string;
  expiresAt: string;
  autoDestroyOnRead: boolean;
  customExpiration?: number; // minutes
}

export interface MessageCleanupResult {
  deletedCount: number;
  errors: string[];
  cleanupTime: number;
}

/**
 * Message Lifecycle Management Service
 * Handles self-destructing messages, automatic cleanup, and secure deletion
 */
class MessageLifecycleService {
  private config: MessageLifecycleConfig = {
    defaultExpirationMinutes: 60, // 1 hour default
    maxExpirationMinutes: 1440, // 24 hours max
    cleanupIntervalMinutes: 5, // Check every 5 minutes
    autoDestroyOnRead: true,
    secureDeletePasses: 3
  };

  private cleanupInterval?: NodeJS.Timeout;
  private isInitialized = false;

  /**
   * Initialize the message lifecycle service
   */
  async initialize(config?: Partial<MessageLifecycleConfig>): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Merge custom config with defaults
    this.config = { ...this.config, ...config };

    // Start automatic cleanup process
    this.startAutomaticCleanup();

    // Set up database triggers for automatic expiration
    await this.setupDatabaseTriggers();

    this.isInitialized = true;
    console.log('Message lifecycle service initialized with config:', this.config);
  }

  /**
   * Create a message with expiration settings
   */
  async createMessageWithExpiration(
    messageData: {
      senderId: string;
      senderDeviceId: string;
      recipientId?: string;
      groupId?: string;
      encryptedContent: string;
      contentSignature: string;
      encryptionMetadata: any;
    },
    expirationMinutes?: number
  ): Promise<{ messageId: string; expiresAt: string }> {
    const session = authService.getCurrentSession();
    if (!session) {
      throw new Error('User must be authenticated');
    }

    // Calculate expiration time
    const expiration = Math.min(
      expirationMinutes || this.config.defaultExpirationMinutes,
      this.config.maxExpirationMinutes
    );
    
    const expiresAt = new Date(Date.now() + expiration * 60 * 1000).toISOString();

    try {
      const { data, error } = await supabase
        .from('messages')
        .insert({
          sender_id: messageData.senderId,
          sender_device_id: messageData.senderDeviceId,
          recipient_id: messageData.recipientId,
          group_id: messageData.groupId,
          encrypted_content: messageData.encryptedContent,
          content_signature: messageData.contentSignature,
          encryption_metadata: messageData.encryptionMetadata,
          expires_at: expiresAt,
          status: 'sending'
        })
        .select('id')
        .single();

      if (error) {
        throw new Error(`Failed to create message: ${error.message}`);
      }

      return {
        messageId: data.id,
        expiresAt
      };
    } catch (error) {
      console.error('Failed to create message with expiration:', error);
      throw error;
    }
  }

  /**
   * Mark message as read and trigger destruction if configured
   */
  async markAsReadAndDestroy(messageId: string): Promise<void> {
    const session = authService.getCurrentSession();
    if (!session) {
      throw new Error('User must be authenticated');
    }

    try {
      // First, mark as read
      const { data: message, error: readError } = await supabase
        .from('messages')
        .update({
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('id', messageId)
        .select('id, auto_destroy_on_read, expires_at')
        .single();

      if (readError) {
        throw new Error(`Failed to mark message as read: ${readError.message}`);
      }

      // If auto-destroy on read is enabled, destroy immediately
      if (this.config.autoDestroyOnRead) {
        await this.destroyMessage(messageId);
      }
    } catch (error) {
      console.error('Failed to mark message as read and destroy:', error);
      throw error;
    }
  }

  /**
   * Destroy a message immediately with secure deletion
   */
  async destroyMessage(messageId: string): Promise<void> {
    try {
      // First, mark as destroyed to prevent further access
      const { error: updateError } = await supabase
        .from('messages')
        .update({
          status: 'destroyed',
          destroyed_at: new Date().toISOString(),
          encrypted_content: '', // Clear content immediately
          content_signature: '',
          encryption_metadata: {}
        })
        .eq('id', messageId);

      if (updateError) {
        console.error('Failed to mark message as destroyed:', updateError);
      }

      // Perform secure deletion with multiple passes
      await this.secureDeleteMessage(messageId);

      console.log(`Message ${messageId} destroyed successfully`);
    } catch (error) {
      console.error('Failed to destroy message:', error);
      throw error;
    }
  }

  /**
   * Perform secure deletion with multiple overwrite passes
   */
  private async secureDeleteMessage(messageId: string): Promise<void> {
    try {
      // Generate random data for overwriting
      const randomData = this.generateRandomData(1024); // 1KB of random data

      // Perform multiple overwrite passes
      for (let pass = 0; pass < this.config.secureDeletePasses; pass++) {
        const overwriteData = this.generateRandomData(1024);
        
        await supabase
          .from('messages')
          .update({
            encrypted_content: overwriteData,
            content_signature: overwriteData.substring(0, 256),
            encryption_metadata: { overwrite_pass: pass + 1 }
          })
          .eq('id', messageId);

        // Small delay between passes
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Final deletion from database
      const { error } = await supabase
        .from('messages')
        .delete()
        .eq('id', messageId);

      if (error) {
        console.error('Failed to delete message from database:', error);
      }
    } catch (error) {
      console.error('Secure deletion failed:', error);
      throw error;
    }
  }

  /**
   * Generate cryptographically secure random data
   */
  private generateRandomData(length: number): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Start automatic cleanup process
   */
  private startAutomaticCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(async () => {
      try {
        await this.performCleanup();
      } catch (error) {
        console.error('Automatic cleanup failed:', error);
      }
    }, this.config.cleanupIntervalMinutes * 60 * 1000);

    console.log(`Automatic cleanup started with ${this.config.cleanupIntervalMinutes} minute intervals`);
  }

  /**
   * Perform cleanup of expired messages
   */
  async performCleanup(): Promise<MessageCleanupResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let deletedCount = 0;

    try {
      // Find expired messages
      const { data: expiredMessages, error: queryError } = await supabase
        .from('messages')
        .select('id, encrypted_content')
        .lt('expires_at', new Date().toISOString())
        .neq('status', 'destroyed')
        .limit(100); // Process in batches

      if (queryError) {
        errors.push(`Failed to query expired messages: ${queryError.message}`);
        return { deletedCount: 0, errors, cleanupTime: Date.now() - startTime };
      }

      if (!expiredMessages || expiredMessages.length === 0) {
        return { deletedCount: 0, errors, cleanupTime: Date.now() - startTime };
      }

      // Destroy each expired message
      for (const message of expiredMessages) {
        try {
          await this.destroyMessage(message.id);
          deletedCount++;
        } catch (error) {
          errors.push(`Failed to destroy message ${message.id}: ${error}`);
        }
      }

      console.log(`Cleanup completed: ${deletedCount} messages destroyed, ${errors.length} errors`);
    } catch (error) {
      errors.push(`Cleanup process failed: ${error}`);
    }

    return {
      deletedCount,
      errors,
      cleanupTime: Date.now() - startTime
    };
  }

  /**
   * Set up database triggers for automatic expiration
   */
  private async setupDatabaseTriggers(): Promise<void> {
    try {
      // Create a function to automatically destroy expired messages
      const triggerFunction = `
        CREATE OR REPLACE FUNCTION auto_destroy_expired_messages()
        RETURNS trigger AS $$
        BEGIN
          -- Check if message has expired
          IF NEW.expires_at <= NOW() AND NEW.status != 'destroyed' THEN
            NEW.status = 'destroyed';
            NEW.destroyed_at = NOW();
            NEW.encrypted_content = '';
            NEW.content_signature = '';
            NEW.encryption_metadata = '{}';
          END IF;
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `;

      // Create trigger that fires on SELECT (when message is accessed)
      const trigger = `
        DROP TRIGGER IF EXISTS trigger_auto_destroy_expired ON messages;
        CREATE TRIGGER trigger_auto_destroy_expired
          BEFORE UPDATE ON messages
          FOR EACH ROW
          EXECUTE FUNCTION auto_destroy_expired_messages();
      `;

      // Execute the SQL (Note: This would typically be done via migrations)
      console.log('Database triggers would be set up via migrations');
      console.log('Trigger function:', triggerFunction);
      console.log('Trigger:', trigger);
    } catch (error) {
      console.error('Failed to setup database triggers:', error);
    }
  }

  /**
   * Get message expiration info
   */
  async getMessageExpiration(messageId: string): Promise<{
    expiresAt: string;
    timeRemaining: number;
    isExpired: boolean;
  } | null> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select('expires_at, status')
        .eq('id', messageId)
        .single();

      if (error || !data) {
        return null;
      }

      const expiresAt = new Date(data.expires_at);
      const now = new Date();
      const timeRemaining = Math.max(0, expiresAt.getTime() - now.getTime());
      const isExpired = timeRemaining === 0 || data.status === 'destroyed';

      return {
        expiresAt: data.expires_at,
        timeRemaining,
        isExpired
      };
    } catch (error) {
      console.error('Failed to get message expiration:', error);
      return null;
    }
  }

  /**
   * Update message expiration time (if not yet read)
   */
  async updateMessageExpiration(messageId: string, newExpirationMinutes: number): Promise<boolean> {
    try {
      const expiration = Math.min(newExpirationMinutes, this.config.maxExpirationMinutes);
      const newExpiresAt = new Date(Date.now() + expiration * 60 * 1000).toISOString();

      const { error } = await supabase
        .from('messages')
        .update({ expires_at: newExpiresAt })
        .eq('id', messageId)
        .is('read_at', null) // Only update if not yet read
        .neq('status', 'destroyed');

      if (error) {
        console.error('Failed to update message expiration:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to update message expiration:', error);
      return false;
    }
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStats(): Promise<{
    totalMessages: number;
    expiredMessages: number;
    destroyedMessages: number;
    nextCleanup: string;
  }> {
    try {
      const [totalResult, expiredResult, destroyedResult] = await Promise.all([
        supabase.from('messages').select('id', { count: 'exact', head: true }),
        supabase.from('messages').select('id', { count: 'exact', head: true })
          .lt('expires_at', new Date().toISOString())
          .neq('status', 'destroyed'),
        supabase.from('messages').select('id', { count: 'exact', head: true })
          .eq('status', 'destroyed')
      ]);

      const nextCleanup = new Date(
        Date.now() + this.config.cleanupIntervalMinutes * 60 * 1000
      ).toISOString();

      return {
        totalMessages: totalResult.count || 0,
        expiredMessages: expiredResult.count || 0,
        destroyedMessages: destroyedResult.count || 0,
        nextCleanup
      };
    } catch (error) {
      console.error('Failed to get cleanup stats:', error);
      return {
        totalMessages: 0,
        expiredMessages: 0,
        destroyedMessages: 0,
        nextCleanup: new Date().toISOString()
      };
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<MessageLifecycleConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart cleanup with new interval if changed
    if (newConfig.cleanupIntervalMinutes) {
      this.startAutomaticCleanup();
    }
    
    console.log('Message lifecycle config updated:', this.config);
  }

  /**
   * Get current configuration
   */
  getConfig(): MessageLifecycleConfig {
    return { ...this.config };
  }

  /**
   * Stop the service and cleanup
   */
  stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }
    
    this.isInitialized = false;
    console.log('Message lifecycle service stopped');
  }
}

export const messageLifecycleService = new MessageLifecycleService();
