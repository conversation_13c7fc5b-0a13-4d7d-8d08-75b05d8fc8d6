import { supabase } from '@/integrations/supabase/client';
import { cryptoService, EncryptedMessage } from './crypto.service';
import { keyManagerService } from './keyManager.service';
import { authService } from './auth.service';
import { messageLifecycleService } from './messageLifecycle.service';

export interface Message {
  id: string;
  content: string;
  senderId: string;
  senderUsername: string;
  recipientId?: string;
  groupId?: string;
  timestamp: string;
  status: 'sent' | 'delivered' | 'read' | 'destroyed';
  isEncrypted: boolean;
  verified: boolean;
}

export interface SendMessageOptions {
  content: string;
  recipientId?: string;
  groupId?: string;
  recipientDeviceId?: string;
  expirationMinutes?: number;
  autoDestroyOnRead?: boolean;
}

/**
 * Messaging Service with Post-Quantum Cryptography
 * Handles secure message sending, receiving, and lifecycle management
 */
class MessagingService {
  /**
   * Send an encrypted message to a user or group
   */
  async sendMessage(options: SendMessageOptions): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const session = authService.getCurrentSession();
      if (!session) {
        return { success: false, error: 'Not authenticated' };
      }

      const deviceKeys = keyManagerService.getCurrentDeviceKeys();
      if (!deviceKeys) {
        return { success: false, error: 'Device keys not initialized' };
      }

      let recipientDevices: Array<{
        deviceId: string;
        kyberPublicKey: Uint8Array;
      }> = [];

      // Get recipient device keys
      if (options.recipientId) {
        // Direct message - get all active devices for the recipient
        const userDevices = await keyManagerService.getUserDeviceKeys(options.recipientId);
        recipientDevices = userDevices.map(device => ({
          deviceId: device.deviceId,
          kyberPublicKey: device.kyberPublicKey
        }));
      } else if (options.groupId) {
        // Group message - get all member devices
        const { data: groupMembers, error } = await supabase
          .from('group_members')
          .select(`
            user_id,
            users!inner(id)
          `)
          .eq('group_id', options.groupId)
          .eq('status', 'active');

        if (error) {
          return { success: false, error: 'Failed to get group members' };
        }

        // Get devices for all group members
        for (const member of groupMembers) {
          const userDevices = await keyManagerService.getUserDeviceKeys(member.user_id);
          recipientDevices.push(...userDevices.map(device => ({
            deviceId: device.deviceId,
            kyberPublicKey: device.kyberPublicKey
          })));
        }
      } else {
        return { success: false, error: 'No recipient specified' };
      }

      if (recipientDevices.length === 0) {
        return { success: false, error: 'No recipient devices found' };
      }

      // Create the message record with expiration using lifecycle service
      const messageResult = await messageLifecycleService.createMessageWithExpiration(
        {
          senderId: session.user.id,
          senderDeviceId: session.device.id,
          recipientId: options.recipientId,
          groupId: options.groupId,
          encryptedContent: '', // Will be updated with encrypted content
          contentSignature: '',
          encryptionMetadata: {}
        },
        options.expirationMinutes
      );

      const messageId = messageResult.messageId;

      // Encrypt message for each recipient device
      const encryptionPromises = recipientDevices.map(async (recipientDevice) => {
        try {
          // Encrypt the message for this specific device
          const encryptedMessage = await cryptoService.encryptMessage(
            options.content,
            recipientDevice.kyberPublicKey,
            deviceKeys.dilithiumKeyPair.privateKey,
            deviceKeys.dilithiumKeyPair.publicKey
          );

          // Store encrypted message for this device
          const { error: recipientError } = await supabase
            .from('message_recipients')
            .insert({
              message_id: messageId,
              device_id: recipientDevice.deviceId,
              encrypted_content: cryptoService.arrayToBase64(encryptedMessage.encryptedContent),
              signature: cryptoService.arrayToBase64(encryptedMessage.signature),
              encryption_metadata: cryptoService.createEncryptionMetadata(encryptedMessage)
            });

          if (recipientError) {
            console.error('Failed to store encrypted message for device:', recipientDevice.deviceId, recipientError);
          }

          return { success: !recipientError, deviceId: recipientDevice.deviceId };
        } catch (error) {
          console.error('Failed to encrypt message for device:', recipientDevice.deviceId, error);
          return { success: false, deviceId: recipientDevice.deviceId };
        }
      });

      const encryptionResults = await Promise.all(encryptionPromises);
      const successfulEncryptions = encryptionResults.filter(result => result.success);

      if (successfulEncryptions.length === 0) {
        // Delete the message if no encryptions succeeded
        await supabase.from('messages').delete().eq('id', messageId);
        return { success: false, error: 'Failed to encrypt message for any recipient' };
      }

      return { success: true, messageId };
    } catch (error) {
      console.error('Failed to send message:', error);
      return { success: false, error: 'Message sending failed' };
    }
  }

  /**
   * Decrypt a single message and mark as read
   */
  async decryptMessage(messageData: any): Promise<Message | null> {
    try {
      const session = authService.getCurrentSession();
      if (!session) {
        return null;
      }

      const deviceKeys = keyManagerService.getCurrentDeviceKeys();
      if (!deviceKeys) {
        return null;
      }

      // Check if message is expired
      const expiration = await messageLifecycleService.getMessageExpiration(messageData.id);
      if (expiration?.isExpired) {
        console.log('Message has expired, cannot decrypt');
        return null;
      }

      // Decrypt the message content
      const decryptedContent = await cryptoService.decryptMessage(
        {
          encryptedContent: messageData.encrypted_content,
          signature: messageData.content_signature,
          metadata: messageData.encryption_metadata
        },
        deviceKeys.kyberKeyPair.privateKey,
        deviceKeys.dilithiumKeyPair.publicKey
      );

      if (!decryptedContent) {
        console.error('Failed to decrypt message content');
        return null;
      }

      // Mark as read and trigger destruction if configured
      await messageLifecycleService.markAsReadAndDestroy(messageData.id);

      // Get sender information
      const { data: senderData } = await supabase
        .from('users')
        .select('username, display_name')
        .eq('id', messageData.sender_id)
        .single();

      return {
        id: messageData.id,
        content: decryptedContent.content,
        senderId: messageData.sender_id,
        senderUsername: senderData?.username || 'Unknown',
        recipientId: messageData.recipient_id,
        groupId: messageData.group_id,
        timestamp: messageData.created_at,
        status: messageData.status,
        isEncrypted: true,
        verified: decryptedContent.verified
      };
    } catch (error) {
      console.error('Failed to decrypt message:', error);
      return null;
    }
  }

  /**
   * Retrieve and decrypt messages for the current device
   */
  async getMessages(options: {
    recipientId?: string;
    groupId?: string;
    limit?: number;
    before?: string;
  } = {}): Promise<Message[]> {
    try {
      const session = authService.getCurrentSession();
      if (!session) {
        return [];
      }

      const deviceKeys = keyManagerService.getCurrentDeviceKeys();
      if (!deviceKeys) {
        return [];
      }

      // Get encrypted messages for this device
      let query = supabase
        .from('message_recipients')
        .select(`
          message_id,
          encrypted_content,
          signature,
          encryption_metadata,
          status,
          messages!inner(
            id,
            sender_id,
            recipient_id,
            group_id,
            created_at,
            users!messages_sender_id_fkey(username)
          )
        `)
        .eq('device_id', session.device.id)
        .neq('status', 'destroyed')
        .order('created_at', { ascending: false, referencedTable: 'messages' });

      if (options.recipientId) {
        query = query.eq('messages.sender_id', options.recipientId);
      }

      if (options.groupId) {
        query = query.eq('messages.group_id', options.groupId);
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data: messageRecipients, error } = await query;

      if (error) {
        console.error('Failed to get messages:', error);
        return [];
      }

      // Decrypt messages
      const decryptedMessages: Message[] = [];

      for (const recipient of messageRecipients) {
        try {
          // Parse encryption metadata
          const metadata = cryptoService.parseEncryptionMetadata(recipient.encryption_metadata);

          // Reconstruct encrypted message
          const encryptedMessage: EncryptedMessage = {
            encryptedContent: cryptoService.base64ToArray(recipient.encrypted_content),
            signature: cryptoService.base64ToArray(recipient.signature),
            senderPublicKey: metadata.senderPublicKey,
            kyberCiphertext: metadata.kyberCiphertext,
            nonce: metadata.nonce
          };

          // Decrypt the message
          const { message: decryptedContent, verified } = await cryptoService.decryptMessage(
            encryptedMessage,
            deviceKeys.kyberKeyPair.privateKey
          );

          decryptedMessages.push({
            id: recipient.message_id,
            content: decryptedContent,
            senderId: recipient.messages.sender_id,
            senderUsername: recipient.messages.users.username,
            recipientId: recipient.messages.recipient_id,
            groupId: recipient.messages.group_id,
            timestamp: recipient.messages.created_at,
            status: recipient.status,
            isEncrypted: true,
            verified
          });
        } catch (error) {
          console.error('Failed to decrypt message:', recipient.message_id, error);
          // Add placeholder for failed decryption
          decryptedMessages.push({
            id: recipient.message_id,
            content: '[Message decryption failed]',
            senderId: recipient.messages.sender_id,
            senderUsername: recipient.messages.users.username,
            recipientId: recipient.messages.recipient_id,
            groupId: recipient.messages.group_id,
            timestamp: recipient.messages.created_at,
            status: recipient.status,
            isEncrypted: true,
            verified: false
          });
        }
      }

      return decryptedMessages;
    } catch (error) {
      console.error('Failed to get messages:', error);
      return [];
    }
  }

  /**
   * Mark a message as read (triggers self-destruction)
   */
  async markMessageAsRead(messageId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const session = authService.getCurrentSession();
      if (!session) {
        return { success: false, error: 'Not authenticated' };
      }

      const { error } = await supabase
        .rpc('mark_message_read', {
          p_message_id: messageId,
          p_device_id: session.device.id
        });

      if (error) {
        console.error('Failed to mark message as read:', error);
        return { success: false, error: 'Failed to mark message as read' };
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to mark message as read:', error);
      return { success: false, error: 'Failed to mark message as read' };
    }
  }

  /**
   * Get conversation participants
   */
  async getConversationParticipants(recipientId?: string, groupId?: string): Promise<Array<{
    id: string;
    username: string;
    displayName: string;
    status: string;
  }>> {
    try {
      if (groupId) {
        // Get group members
        const { data: members, error } = await supabase
          .from('group_members')
          .select(`
            users!inner(
              id,
              username,
              display_name,
              status
            )
          `)
          .eq('group_id', groupId)
          .eq('status', 'active');

        if (error) {
          console.error('Failed to get group members:', error);
          return [];
        }

        return members.map(member => ({
          id: member.users.id,
          username: member.users.username,
          displayName: member.users.display_name || member.users.username,
          status: member.users.status
        }));
      } else if (recipientId) {
        // Get single user
        const { data: user, error } = await supabase
          .from('users')
          .select('id, username, display_name, status')
          .eq('id', recipientId)
          .single();

        if (error || !user) {
          console.error('Failed to get user:', error);
          return [];
        }

        return [{
          id: user.id,
          username: user.username,
          displayName: user.display_name || user.username,
          status: user.status
        }];
      }

      return [];
    } catch (error) {
      console.error('Failed to get conversation participants:', error);
      return [];
    }
  }
}

export const messagingService = new MessagingService();
