import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { authService } from './auth.service';
import { messagingService } from './messaging.service';

export interface TypingIndicator {
  userId: string;
  username: string;
  conversationId: string;
  isTyping: boolean;
  timestamp: number;
}

export interface PresenceUser {
  userId: string;
  username: string;
  status: 'online' | 'away' | 'offline';
  lastSeen: string;
  deviceId: string;
}

export interface MessageDeliveryStatus {
  messageId: string;
  recipientId: string;
  status: 'sent' | 'delivered' | 'read' | 'destroyed';
  timestamp: string;
}

export interface RealtimeCallbacks {
  onNewMessage?: (message: any) => void;
  onMessageStatusUpdate?: (status: MessageDeliveryStatus) => void;
  onTypingIndicator?: (indicator: TypingIndicator) => void;
  onPresenceUpdate?: (users: PresenceUser[]) => void;
  onConnectionStateChange?: (state: 'connected' | 'disconnected' | 'reconnecting') => void;
}

/**
 * Real-time Messaging Service with Supabase Realtime
 * Handles instant messaging, typing indicators, presence, and delivery confirmations
 */
class RealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map();
  private callbacks: RealtimeCallbacks = {};
  private connectionState: 'connected' | 'disconnected' | 'reconnecting' = 'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private presenceHeartbeatInterval?: NodeJS.Timeout;

  /**
   * Initialize real-time connections
   */
  async initialize(callbacks: RealtimeCallbacks): Promise<void> {
    this.callbacks = callbacks;
    
    const session = authService.getCurrentSession();
    if (!session) {
      throw new Error('User must be authenticated to initialize real-time messaging');
    }

    try {
      // Subscribe to messages for the current user
      await this.subscribeToMessages(session.user.id);
      
      // Subscribe to message status updates
      await this.subscribeToMessageStatus(session.user.id);
      
      // Initialize presence tracking
      await this.initializePresence(session.user.id);
      
      // Start connection monitoring
      this.startConnectionMonitoring();
      
      this.connectionState = 'connected';
      this.callbacks.onConnectionStateChange?.('connected');
      
      console.log('Real-time messaging initialized successfully');
    } catch (error) {
      console.error('Failed to initialize real-time messaging:', error);
      this.connectionState = 'disconnected';
      this.callbacks.onConnectionStateChange?.('disconnected');
      throw error;
    }
  }

  /**
   * Subscribe to new messages for the current user
   */
  private async subscribeToMessages(userId: string): Promise<void> {
    const channelName = `messages:${userId}`;
    
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `recipient_id=eq.${userId}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          this.handleNewMessage(payload.new);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `sender_id=eq.${userId}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          this.handleMessageSent(payload.new);
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Subscribed to messages channel: ${channelName}`);
        } else if (status === 'CHANNEL_ERROR') {
          console.error(`Error subscribing to messages channel: ${channelName}`);
          this.handleConnectionError();
        }
      });

    this.channels.set(channelName, channel);
  }

  /**
   * Subscribe to message status updates (delivery confirmations, read receipts)
   */
  private async subscribeToMessageStatus(userId: string): Promise<void> {
    const channelName = `message_status:${userId}`;
    
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `sender_id=eq.${userId}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          this.handleMessageStatusUpdate(payload.new);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'message_recipients',
          filter: `recipient_id=eq.${userId}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          this.handleRecipientStatusUpdate(payload.new);
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Subscribed to message status channel: ${channelName}`);
        } else if (status === 'CHANNEL_ERROR') {
          console.error(`Error subscribing to message status channel: ${channelName}`);
          this.handleConnectionError();
        }
      });

    this.channels.set(channelName, channel);
  }

  /**
   * Initialize presence tracking for online/offline status
   */
  private async initializePresence(userId: string): Promise<void> {
    const session = authService.getCurrentSession();
    if (!session) return;

    const channelName = 'presence:global';
    
    const channel = supabase
      .channel(channelName)
      .on('presence', { event: 'sync' }, () => {
        const presenceState = channel.presenceState();
        const users = this.parsePresenceState(presenceState);
        this.callbacks.onPresenceUpdate?.(users);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('User joined:', key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('User left:', key, leftPresences);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          // Track current user's presence
          await channel.track({
            userId: session.user.id,
            username: session.user.user_metadata?.username || 'Unknown',
            status: 'online',
            lastSeen: new Date().toISOString(),
            deviceId: session.device?.id || 'unknown'
          });
          
          console.log(`Subscribed to presence channel: ${channelName}`);
        }
      });

    this.channels.set(channelName, channel);
    
    // Send periodic heartbeat to maintain presence
    this.presenceHeartbeatInterval = setInterval(() => {
      channel.track({
        userId: session.user.id,
        username: session.user.user_metadata?.username || 'Unknown',
        status: 'online',
        lastSeen: new Date().toISOString(),
        deviceId: session.device?.id || 'unknown'
      });
    }, 30000); // Every 30 seconds
  }

  /**
   * Subscribe to typing indicators for a specific conversation
   */
  async subscribeToTypingIndicators(conversationId: string): Promise<void> {
    const channelName = `typing:${conversationId}`;
    
    if (this.channels.has(channelName)) {
      return; // Already subscribed
    }

    const channel = supabase
      .channel(channelName)
      .on('broadcast', { event: 'typing' }, (payload) => {
        const indicator: TypingIndicator = payload.payload;
        this.callbacks.onTypingIndicator?.(indicator);
        
        // Auto-clear typing indicator after 3 seconds
        const timeoutKey = `${indicator.userId}:${conversationId}`;
        if (this.typingTimeouts.has(timeoutKey)) {
          clearTimeout(this.typingTimeouts.get(timeoutKey)!);
        }
        
        if (indicator.isTyping) {
          const timeout = setTimeout(() => {
            this.callbacks.onTypingIndicator?.({
              ...indicator,
              isTyping: false,
              timestamp: Date.now()
            });
            this.typingTimeouts.delete(timeoutKey);
          }, 3000);
          
          this.typingTimeouts.set(timeoutKey, timeout);
        }
      })
      .subscribe();

    this.channels.set(channelName, channel);
  }

  /**
   * Send typing indicator
   */
  async sendTypingIndicator(conversationId: string, isTyping: boolean): Promise<void> {
    const session = authService.getCurrentSession();
    if (!session) return;

    const channelName = `typing:${conversationId}`;
    const channel = this.channels.get(channelName);
    
    if (!channel) {
      await this.subscribeToTypingIndicators(conversationId);
      return this.sendTypingIndicator(conversationId, isTyping);
    }

    const indicator: TypingIndicator = {
      userId: session.user.id,
      username: session.user.user_metadata?.username || 'Unknown',
      conversationId,
      isTyping,
      timestamp: Date.now()
    };

    await channel.send({
      type: 'broadcast',
      event: 'typing',
      payload: indicator
    });
  }

  /**
   * Mark message as delivered
   */
  async markMessageAsDelivered(messageId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('messages')
        .update({ 
          status: 'delivered',
          delivered_at: new Date().toISOString()
        })
        .eq('id', messageId);

      if (error) {
        console.error('Failed to mark message as delivered:', error);
      }
    } catch (error) {
      console.error('Error marking message as delivered:', error);
    }
  }

  /**
   * Mark message as read and trigger self-destruction
   */
  async markMessageAsRead(messageId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('messages')
        .update({ 
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('id', messageId);

      if (error) {
        console.error('Failed to mark message as read:', error);
      }
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }

  /**
   * Handle new incoming message
   */
  private async handleNewMessage(message: any): Promise<void> {
    try {
      // Automatically mark as delivered
      await this.markMessageAsDelivered(message.id);
      
      // Decrypt and process the message
      const decryptedMessage = await messagingService.decryptMessage(message);
      
      if (decryptedMessage) {
        this.callbacks.onNewMessage?.(decryptedMessage);
      }
    } catch (error) {
      console.error('Failed to handle new message:', error);
    }
  }

  /**
   * Handle message sent confirmation
   */
  private handleMessageSent(message: any): void {
    const status: MessageDeliveryStatus = {
      messageId: message.id,
      recipientId: message.recipient_id,
      status: 'sent',
      timestamp: message.created_at
    };
    
    this.callbacks.onMessageStatusUpdate?.(status);
  }

  /**
   * Handle message status updates
   */
  private handleMessageStatusUpdate(message: any): void {
    const status: MessageDeliveryStatus = {
      messageId: message.id,
      recipientId: message.recipient_id,
      status: message.status,
      timestamp: message.read_at || message.delivered_at || message.created_at
    };
    
    this.callbacks.onMessageStatusUpdate?.(status);
  }

  /**
   * Handle recipient status updates for group messages
   */
  private handleRecipientStatusUpdate(recipient: any): void {
    const status: MessageDeliveryStatus = {
      messageId: recipient.message_id,
      recipientId: recipient.recipient_id,
      status: recipient.status,
      timestamp: recipient.read_at || recipient.delivered_at
    };
    
    this.callbacks.onMessageStatusUpdate?.(status);
  }

  /**
   * Parse presence state into user list
   */
  private parsePresenceState(presenceState: any): PresenceUser[] {
    const users: PresenceUser[] = [];
    
    Object.keys(presenceState).forEach(key => {
      const presences = presenceState[key];
      if (presences && presences.length > 0) {
        const presence = presences[0];
        users.push({
          userId: presence.userId,
          username: presence.username,
          status: presence.status,
          lastSeen: presence.lastSeen,
          deviceId: presence.deviceId
        });
      }
    });
    
    return users;
  }

  /**
   * Start connection monitoring and auto-reconnection
   */
  private startConnectionMonitoring(): void {
    // Monitor Supabase connection state
    supabase.realtime.onOpen(() => {
      console.log('Supabase Realtime connection opened');
      this.connectionState = 'connected';
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      this.callbacks.onConnectionStateChange?.('connected');
    });

    supabase.realtime.onClose(() => {
      console.log('Supabase Realtime connection closed');
      this.connectionState = 'disconnected';
      this.callbacks.onConnectionStateChange?.('disconnected');
      this.attemptReconnection();
    });

    supabase.realtime.onError((error) => {
      console.error('Supabase Realtime error:', error);
      this.handleConnectionError();
    });
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(): void {
    this.connectionState = 'disconnected';
    this.callbacks.onConnectionStateChange?.('disconnected');
    this.attemptReconnection();
  }

  /**
   * Attempt to reconnect with exponential backoff
   */
  private attemptReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.connectionState = 'reconnecting';
    this.callbacks.onConnectionStateChange?.('reconnecting');
    this.reconnectAttempts++;

    setTimeout(async () => {
      try {
        console.log(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        
        // Cleanup existing channels
        this.cleanup();
        
        // Re-initialize connections
        await this.initialize(this.callbacks);
        
      } catch (error) {
        console.error('Reconnection failed:', error);
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000); // Max 30 seconds
        this.attemptReconnection();
      }
    }, this.reconnectDelay);
  }

  /**
   * Get current connection state
   */
  getConnectionState(): 'connected' | 'disconnected' | 'reconnecting' {
    return this.connectionState;
  }

  /**
   * Cleanup all subscriptions and timers
   */
  cleanup(): void {
    // Unsubscribe from all channels
    this.channels.forEach((channel) => {
      supabase.removeChannel(channel);
    });
    this.channels.clear();

    // Clear typing timeouts
    this.typingTimeouts.forEach((timeout) => {
      clearTimeout(timeout);
    });
    this.typingTimeouts.clear();

    // Clear presence heartbeat
    if (this.presenceHeartbeatInterval) {
      clearInterval(this.presenceHeartbeatInterval);
      this.presenceHeartbeatInterval = undefined;
    }

    this.connectionState = 'disconnected';
    console.log('Real-time service cleaned up');
  }
}

export const realtimeService = new RealtimeService();
