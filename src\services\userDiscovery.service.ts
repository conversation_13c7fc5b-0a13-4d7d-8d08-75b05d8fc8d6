import { supabase } from '@/integrations/supabase/client';

export interface UserValidationResult {
  exists: boolean;
  canReceiveMessages: boolean;
  error?: string;
}

export interface UserDiscoveryOptions {
  validateOnly?: boolean; // If true, only validates existence without revealing any info
  checkMessageability?: boolean; // If true, checks if user can receive messages
}

class UserDiscoveryService {
  /**
   * Validate if a username exists and can receive messages
   * This method maintains anonymity by not revealing any user information
   * beyond basic existence and message capability
   */
  async validateUsername(
    username: string,
    options: UserDiscoveryOptions = {}
  ): Promise<UserValidationResult> {
    try {
      const { validateOnly = true, checkMessageability = true } = options;

      if (!username || username.trim().length === 0) {
        return {
          exists: false,
          canReceiveMessages: false,
          error: 'Username is required'
        };
      }

      const trimmedUsername = username.trim();

      // Use a database function to validate username without revealing information
      const { data, error } = await supabase.rpc('validate_username_for_messaging', {
        p_username: trimmedUsername,
        p_check_messageability: checkMessageability
      });

      if (error) {
        console.error('Username validation error:', error);
        return {
          exists: false,
          canReceiveMessages: false,
          error: 'Unable to validate username'
        };
      }

      if (!data || data.length === 0) {
        return {
          exists: false,
          canReceiveMessages: false,
          error: 'Username not found'
        };
      }

      const result = data[0];
      return {
        exists: result.user_exists,
        canReceiveMessages: result.can_receive_messages,
        error: result.user_exists ? undefined : 'Username not found'
      };
    } catch (error) {
      console.error('Error validating username:', error);
      return {
        exists: false,
        canReceiveMessages: false,
        error: 'Username validation failed'
      };
    }
  }

  /**
   * Validate username for message sending
   * Returns appropriate error messages without revealing user information
   */
  async validateRecipient(username: string): Promise<{
    valid: boolean;
    error?: string;
    suggestion?: string;
  }> {
    try {
      const result = await this.validateUsername(username, {
        validateOnly: false,
        checkMessageability: true
      });

      if (!result.exists) {
        return {
          valid: false,
          error: 'Username not found. Please verify the username and try again.',
          suggestion: 'Double-check the spelling and ensure the user has an active account.'
        };
      }

      if (!result.canReceiveMessages) {
        return {
          valid: false,
          error: 'Unable to send message to this user.',
          suggestion: 'The user may have restricted messaging or their account may be inactive.'
        };
      }

      return {
        valid: true
      };
    } catch (error) {
      console.error('Error validating recipient:', error);
      return {
        valid: false,
        error: 'Unable to validate recipient. Please try again.',
        suggestion: 'Check your connection and try again.'
      };
    }
  }

  /**
   * Check if current user can send messages to a specific username
   * This includes checking blocks, restrictions, etc.
   */
  async canSendMessageTo(recipientUsername: string): Promise<{
    canSend: boolean;
    reason?: string;
  }> {
    try {
      const { data, error } = await supabase.rpc('can_send_message_to_user', {
        p_recipient_username: recipientUsername
      });

      if (error) {
        console.error('Error checking message permissions:', error);
        return {
          canSend: false,
          reason: 'Unable to verify messaging permissions'
        };
      }

      if (!data || data.length === 0) {
        return {
          canSend: false,
          reason: 'Recipient not found'
        };
      }

      const result = data[0];
      return {
        canSend: result.can_send,
        reason: result.can_send ? undefined : result.reason
      };
    } catch (error) {
      console.error('Error checking message permissions:', error);
      return {
        canSend: false,
        reason: 'Permission check failed'
      };
    }
  }

  /**
   * Sanitize username input to prevent injection attacks
   */
  sanitizeUsername(username: string): string {
    if (!username) return '';
    
    return username
      .trim()
      .toLowerCase()
      .replace(/[^a-z0-9_-]/g, '') // Only allow alphanumeric, underscore, and hyphen
      .substring(0, 50); // Limit length
  }

  /**
   * Validate username format without checking existence
   */
  validateUsernameFormat(username: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    if (!username || username.trim().length === 0) {
      errors.push('Username is required');
    } else {
      const trimmed = username.trim();
      
      if (trimmed.length < 3) {
        errors.push('Username must be at least 3 characters long');
      }
      
      if (trimmed.length > 50) {
        errors.push('Username must be less than 50 characters');
      }
      
      if (!/^[a-zA-Z0-9_-]+$/.test(trimmed)) {
        errors.push('Username can only contain letters, numbers, underscores, and hyphens');
      }
      
      if (/^[_-]/.test(trimmed) || /[_-]$/.test(trimmed)) {
        errors.push('Username cannot start or end with underscore or hyphen');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get appropriate error message for failed username validation
   * This maintains anonymity by providing generic error messages
   */
  getGenericErrorMessage(error: string): string {
    // Map specific errors to generic messages to maintain anonymity
    const errorMap: Record<string, string> = {
      'Username not found': 'Unable to find the specified user. Please verify the username.',
      'User inactive': 'Unable to send message to this user.',
      'User suspended': 'Unable to send message to this user.',
      'Messaging disabled': 'Unable to send message to this user.',
      'User blocked': 'Unable to send message to this user.',
      'Permission denied': 'Unable to send message to this user.'
    };

    return errorMap[error] || 'Unable to validate username. Please try again.';
  }
}

export const userDiscoveryService = new UserDiscoveryService();
