// Dynamic imports used to avoid build-time WASM issues

// Performance monitoring interface
interface PerformanceMetrics {
  keyGeneration: number;
  encryption: number;
  decryption: number;
  signing: number;
  verification: number;
}

// Hardware acceleration detection
interface HardwareCapabilities {
  hasWebAssembly: boolean;
  hasSharedArrayBuffer: boolean;
  hasAtomics: boolean;
  hasSIMD: boolean;
  workerSupport: boolean;
  memoryLimit: number;
}

// WASM module cache
interface WASMModuleCache {
  kyber?: any;
  dilithium?: any;
  initialized: boolean;
}

export interface WASMKeyPair {
  publicKey: Uint8Array;
  privateKey: Uint8Array;
}

export interface WASMEncryptedMessage {
  ciphertext: Uint8Array;
  sharedSecret: Uint8Array;
  signature: Uint8Array;
  nonce: Uint8Array;
  metadata: {
    algorithm: string;
    keySize: number;
    timestamp: number;
    version: string;
  };
}

export interface WASMDecryptedMessage {
  message: string;
  verified: boolean;
  performance: {
    decryptionTime: number;
    verificationTime: number;
  };
}

class WASMCryptoService {
  private moduleCache: WASMModuleCache = { initialized: false };
  private performanceMetrics: PerformanceMetrics = {
    keyGeneration: 0,
    encryption: 0,
    decryption: 0,
    signing: 0,
    verification: 0
  };
  private hardwareCapabilities: HardwareCapabilities;
  private workerPool: Worker[] = [];
  private maxWorkers = 4;

  constructor() {
    this.hardwareCapabilities = this.detectHardwareCapabilities();
    this.initializeWorkerPool();
  }

  /**
   * Detect hardware capabilities for optimization
   */
  private detectHardwareCapabilities(): HardwareCapabilities {
    const capabilities: HardwareCapabilities = {
      hasWebAssembly: typeof WebAssembly !== 'undefined',
      hasSharedArrayBuffer: typeof SharedArrayBuffer !== 'undefined',
      hasAtomics: typeof Atomics !== 'undefined',
      hasSIMD: false,
      workerSupport: typeof Worker !== 'undefined',
      memoryLimit: this.getMemoryLimit()
    };

    // Test for SIMD support
    try {
      if (WebAssembly && WebAssembly.validate) {
        // Simple SIMD test bytecode
        const simdTest = new Uint8Array([
          0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00
        ]);
        capabilities.hasSIMD = WebAssembly.validate(simdTest);
      }
    } catch (e) {
      capabilities.hasSIMD = false;
    }

    return capabilities;
  }

  /**
   * Get available memory limit
   */
  private getMemoryLimit(): number {
    if ('memory' in performance && 'usedJSHeapSize' in (performance as any).memory) {
      return (performance as any).memory.jsHeapSizeLimit || 2147483648; // 2GB default
    }
    return 2147483648; // 2GB default
  }

  /**
   * Initialize worker pool for parallel processing
   * Disabled due to external script loading issues
   */
  private initializeWorkerPool(): void {
    // Disable worker pool to avoid external script loading issues
    console.warn('Worker pool disabled due to external script loading issues');
    return;
  }

  /**
   * Initialize WASM modules with performance optimization
   * Disabled due to external dependency issues
   */
  async initializeModules(): Promise<void> {
    if (this.moduleCache.initialized) return;

    console.warn('WASM modules disabled due to external dependency issues');

    // Mark as initialized but without actual modules to prevent repeated attempts
    this.moduleCache.initialized = true;
  }

  /**
   * Generate Kyber key pair with hardware acceleration
   * Disabled - throws error to force fallback to JS implementation
   */
  async generateKyberKeyPair(): Promise<WASMKeyPair> {
    throw new Error('WASM Kyber disabled - use JS fallback');
  }

  /**
   * Generate Dilithium key pair with hardware acceleration
   * Disabled - throws error to force fallback to JS implementation
   */
  async generateDilithiumKeyPair(): Promise<WASMKeyPair> {
    throw new Error('WASM Dilithium disabled - use JS fallback');
  }

  /**
   * Perform Kyber encapsulation with optimization
   * Disabled - throws error to force fallback to JS implementation
   */
  async kyberEncapsulate(publicKey: Uint8Array): Promise<{ ciphertext: Uint8Array; sharedSecret: Uint8Array }> {
    throw new Error('WASM Kyber encapsulation disabled - use JS fallback');
  }

  /**
   * Perform Kyber decapsulation with optimization
   * Disabled - throws error to force fallback to JS implementation
   */
  async kyberDecapsulate(ciphertext: Uint8Array, privateKey: Uint8Array): Promise<Uint8Array> {
    throw new Error('WASM Kyber decapsulation disabled - use JS fallback');
  }


  /**
   * Sign message with Dilithium
   * Disabled - throws error to force fallback to JS implementation
   */
  async dilithiumSign(message: Uint8Array, privateKey: Uint8Array): Promise<Uint8Array> {
    throw new Error('WASM Dilithium signing disabled - use JS fallback');
  }

  /**
   * Verify signature with Dilithium
   * Disabled - throws error to force fallback to JS implementation
   */
  async dilithiumVerify(message: Uint8Array, signature: Uint8Array, publicKey: Uint8Array): Promise<boolean> {
    throw new Error('WASM Dilithium verification disabled - use JS fallback');
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Get hardware capabilities
   */
  getHardwareCapabilities(): HardwareCapabilities {
    return { ...this.hardwareCapabilities };
  }

  /**
   * Reset performance metrics
   */
  resetPerformanceMetrics(): void {
    this.performanceMetrics = {
      keyGeneration: 0,
      encryption: 0,
      decryption: 0,
      signing: 0,
      verification: 0
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    // Terminate worker pool
    this.workerPool.forEach(worker => {
      worker.terminate();
    });
    this.workerPool = [];
    
    // Reset cache
    this.moduleCache = { initialized: false };
  }
}

// Export singleton instance
export const wasmCryptoService = new WASMCryptoService();
