/**
 * Hardware Acceleration Detection and Optimization Utilities
 * Detects browser capabilities for post-quantum cryptography optimization
 */

export interface HardwareCapabilities {
  // WebAssembly support
  hasWebAssembly: boolean;
  wasmVersion: string;
  
  // SIMD support
  hasSIMD: boolean;
  simdInstructions: string[];
  
  // Threading support
  hasSharedArrayBuffer: boolean;
  hasAtomics: boolean;
  workerSupport: boolean;
  maxWorkers: number;
  
  // Memory capabilities
  memoryLimit: number;
  availableMemory: number;
  
  // CPU information
  cpuCores: number;
  cpuArchitecture: string;
  
  // Browser optimizations
  hasJIT: boolean;
  hasOptimizedArrays: boolean;
  
  // Crypto-specific features
  hasWebCrypto: boolean;
  hasSubtleCrypto: boolean;
  supportedAlgorithms: string[];
}

export interface PerformanceProfile {
  level: 'low' | 'medium' | 'high' | 'ultra';
  recommendedMode: 'security' | 'balanced' | 'speed';
  optimizations: string[];
  limitations: string[];
}

/**
 * Detect comprehensive hardware capabilities
 */
export async function detectHardwareCapabilities(): Promise<HardwareCapabilities> {
  const capabilities: HardwareCapabilities = {
    hasWebAssembly: false,
    wasmVersion: 'none',
    hasSIMD: false,
    simdInstructions: [],
    hasSharedArrayBuffer: false,
    hasAtomics: false,
    workerSupport: false,
    maxWorkers: 0,
    memoryLimit: 0,
    availableMemory: 0,
    cpuCores: 1,
    cpuArchitecture: 'unknown',
    hasJIT: false,
    hasOptimizedArrays: false,
    hasWebCrypto: false,
    hasSubtleCrypto: false,
    supportedAlgorithms: []
  };

  // WebAssembly detection
  if (typeof WebAssembly !== 'undefined') {
    capabilities.hasWebAssembly = true;
    capabilities.wasmVersion = '1.0';
    
    // Test for WASM features
    try {
      // Test for bulk memory operations
      const bulkMemoryTest = new Uint8Array([
        0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00,
        0x05, 0x03, 0x01, 0x00, 0x01, 0x0c, 0x01, 0x00
      ]);
      if (WebAssembly.validate(bulkMemoryTest)) {
        capabilities.wasmVersion = '1.1';
      }
    } catch (e) {
      // Bulk memory not supported
    }

    // SIMD detection
    try {
      const simdTest = new Uint8Array([
        0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00,
        0x01, 0x05, 0x01, 0x60, 0x00, 0x01, 0x7b,
        0x03, 0x02, 0x01, 0x00, 0x0a, 0x07, 0x01, 0x05,
        0x00, 0x41, 0x00, 0xfd, 0x0f, 0x0b
      ]);
      
      if (WebAssembly.validate(simdTest)) {
        capabilities.hasSIMD = true;
        capabilities.simdInstructions = ['v128', 'i32x4', 'f32x4', 'i64x2', 'f64x2'];
      }
    } catch (e) {
      // SIMD not supported
    }
  }

  // Threading capabilities
  capabilities.hasSharedArrayBuffer = typeof SharedArrayBuffer !== 'undefined';
  capabilities.hasAtomics = typeof Atomics !== 'undefined';
  capabilities.workerSupport = typeof Worker !== 'undefined';
  
  if (capabilities.workerSupport) {
    // Estimate max workers based on CPU cores
    capabilities.maxWorkers = Math.min(navigator.hardwareConcurrency || 4, 8);
  }

  // Memory detection
  if ('memory' in performance && 'usedJSHeapSize' in (performance as any).memory) {
    const memInfo = (performance as any).memory;
    capabilities.memoryLimit = memInfo.jsHeapSizeLimit || 2147483648;
    capabilities.availableMemory = capabilities.memoryLimit - memInfo.usedJSHeapSize;
  } else {
    capabilities.memoryLimit = 2147483648; // 2GB default
    capabilities.availableMemory = 1073741824; // 1GB default
  }

  // CPU information
  capabilities.cpuCores = navigator.hardwareConcurrency || 1;
  capabilities.cpuArchitecture = detectCPUArchitecture();

  // JavaScript engine optimizations
  capabilities.hasJIT = detectJITSupport();
  capabilities.hasOptimizedArrays = detectOptimizedArrays();

  // Web Crypto API
  capabilities.hasWebCrypto = typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined';
  if (capabilities.hasWebCrypto) {
    capabilities.hasSubtleCrypto = true;
    capabilities.supportedAlgorithms = await detectSupportedCryptoAlgorithms();
  }

  return capabilities;
}

/**
 * Detect CPU architecture
 */
function detectCPUArchitecture(): string {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (userAgent.includes('arm64') || userAgent.includes('aarch64')) {
    return 'arm64';
  } else if (userAgent.includes('arm')) {
    return 'arm32';
  } else if (userAgent.includes('x86_64') || userAgent.includes('amd64')) {
    return 'x86_64';
  } else if (userAgent.includes('x86') || userAgent.includes('i386') || userAgent.includes('i686')) {
    return 'x86';
  }
  
  return 'unknown';
}

/**
 * Detect JIT compilation support
 */
function detectJITSupport(): boolean {
  try {
    // Test function optimization
    const testFunc = function(x: number) { return x * x; };
    
    // Run function many times to trigger JIT
    for (let i = 0; i < 10000; i++) {
      testFunc(i);
    }
    
    // Check if function was optimized (heuristic)
    return testFunc.toString().length < 100; // Optimized functions often have shorter toString
  } catch (e) {
    return false;
  }
}

/**
 * Detect optimized array operations
 */
function detectOptimizedArrays(): boolean {
  try {
    // Test typed array performance
    const arr = new Uint32Array(1000);
    const start = performance.now();
    
    for (let i = 0; i < arr.length; i++) {
      arr[i] = i * 2;
    }
    
    const end = performance.now();
    
    // If operation is very fast, arrays are likely optimized
    return (end - start) < 1; // Less than 1ms for 1000 operations
  } catch (e) {
    return false;
  }
}

/**
 * Detect supported crypto algorithms
 */
async function detectSupportedCryptoAlgorithms(): Promise<string[]> {
  const algorithms: string[] = [];
  
  if (!crypto.subtle) return algorithms;
  
  const testAlgorithms = [
    'AES-GCM',
    'AES-CBC',
    'RSA-OAEP',
    'ECDSA',
    'ECDH',
    'HMAC',
    'PBKDF2',
    'SHA-256',
    'SHA-384',
    'SHA-512'
  ];
  
  for (const alg of testAlgorithms) {
    try {
      // Test key generation for each algorithm
      switch (alg) {
        case 'AES-GCM':
        case 'AES-CBC':
          await crypto.subtle.generateKey(
            { name: alg, length: 256 },
            false,
            ['encrypt', 'decrypt']
          );
          algorithms.push(alg);
          break;
        case 'ECDSA':
          await crypto.subtle.generateKey(
            { name: alg, namedCurve: 'P-256' },
            false,
            ['sign', 'verify']
          );
          algorithms.push(alg);
          break;
        case 'HMAC':
          await crypto.subtle.generateKey(
            { name: alg, hash: 'SHA-256' },
            false,
            ['sign', 'verify']
          );
          algorithms.push(alg);
          break;
        default:
          // For hash algorithms, just add them
          if (alg.startsWith('SHA-')) {
            algorithms.push(alg);
          }
      }
    } catch (e) {
      // Algorithm not supported
    }
  }
  
  return algorithms;
}

/**
 * Generate performance profile based on capabilities
 */
export function generatePerformanceProfile(capabilities: HardwareCapabilities): PerformanceProfile {
  const profile: PerformanceProfile = {
    level: 'low',
    recommendedMode: 'security',
    optimizations: [],
    limitations: []
  };

  let score = 0;

  // Score based on capabilities
  if (capabilities.hasWebAssembly) score += 20;
  if (capabilities.hasSIMD) score += 15;
  if (capabilities.hasSharedArrayBuffer && capabilities.hasAtomics) score += 10;
  if (capabilities.workerSupport) score += 10;
  if (capabilities.cpuCores >= 4) score += 10;
  if (capabilities.memoryLimit >= 4294967296) score += 10; // 4GB+
  if (capabilities.hasJIT) score += 10;
  if (capabilities.hasOptimizedArrays) score += 5;
  if (capabilities.hasWebCrypto) score += 10;

  // Determine performance level
  if (score >= 70) {
    profile.level = 'ultra';
    profile.recommendedMode = 'speed';
  } else if (score >= 50) {
    profile.level = 'high';
    profile.recommendedMode = 'speed';
  } else if (score >= 30) {
    profile.level = 'medium';
    profile.recommendedMode = 'balanced';
  } else {
    profile.level = 'low';
    profile.recommendedMode = 'security';
  }

  // Add optimizations
  if (capabilities.hasWebAssembly) {
    profile.optimizations.push('WASM acceleration');
  }
  if (capabilities.hasSIMD) {
    profile.optimizations.push('SIMD vectorization');
  }
  if (capabilities.workerSupport && capabilities.maxWorkers > 1) {
    profile.optimizations.push(`Parallel processing (${capabilities.maxWorkers} workers)`);
  }
  if (capabilities.hasWebCrypto) {
    profile.optimizations.push('Hardware crypto acceleration');
  }

  // Add limitations
  if (!capabilities.hasWebAssembly) {
    profile.limitations.push('No WASM support - using JS fallback');
  }
  if (!capabilities.hasSIMD) {
    profile.limitations.push('No SIMD support - scalar operations only');
  }
  if (!capabilities.hasSharedArrayBuffer) {
    profile.limitations.push('No SharedArrayBuffer - limited parallelization');
  }
  if (capabilities.memoryLimit < 1073741824) { // 1GB
    profile.limitations.push('Limited memory - may affect large operations');
  }

  return profile;
}

/**
 * Benchmark crypto operations
 */
export async function benchmarkCryptoOperations(): Promise<{
  keyGeneration: number;
  encryption: number;
  hashing: number;
}> {
  const results = {
    keyGeneration: 0,
    encryption: 0,
    hashing: 0
  };

  if (!crypto.subtle) return results;

  try {
    // Benchmark key generation
    const keyGenStart = performance.now();
    await crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
    results.keyGeneration = performance.now() - keyGenStart;

    // Benchmark encryption
    const key = await crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
    const data = new Uint8Array(1024); // 1KB test data
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    const encStart = performance.now();
    await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      data
    );
    results.encryption = performance.now() - encStart;

    // Benchmark hashing
    const hashStart = performance.now();
    await crypto.subtle.digest('SHA-256', data);
    results.hashing = performance.now() - hashStart;

  } catch (e) {
    console.warn('Crypto benchmark failed:', e);
  }

  return results;
}
