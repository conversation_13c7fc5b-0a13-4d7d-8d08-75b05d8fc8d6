-- Crystal Chat Database Schema
-- Post-Quantum Secure Messaging Application
-- Created: 2025-01-27

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Custom types for the application
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE message_status AS ENUM ('sending', 'sent', 'delivered', 'read', 'expired', 'destroyed');
CREATE TYPE device_status AS ENUM ('active', 'inactive', 'revoked');
CREATE TYPE group_role AS ENUM ('owner', 'admin', 'member');

-- Users table - Core user information
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    secret_word_hash TEXT NOT NULL, -- Hashed secret word for authentication
    display_name VARCHA<PERSON>(100),
    status user_status DEFAULT 'active',
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Security constraints
    CONSTRAINT username_length CHECK (char_length(username) >= 3),
    CONSTRAINT display_name_length CHECK (char_length(display_name) >= 1)
);

-- Devices table - Multi-device support with individual keys
CREATE TABLE devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_name VARCHAR(100) NOT NULL,
    device_fingerprint TEXT UNIQUE NOT NULL, -- Unique device identifier
    public_key_dilithium TEXT NOT NULL, -- Post-quantum signature public key
    public_key_kyber TEXT NOT NULL, -- Post-quantum encryption public key
    status device_status DEFAULT 'active',
    last_used TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT device_name_length CHECK (char_length(device_name) >= 1)
);

-- Groups table - Secure group conversations
CREATE TABLE groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    group_key_kyber TEXT NOT NULL, -- Post-quantum group encryption key
    max_members INTEGER DEFAULT 50,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT group_name_length CHECK (char_length(name) >= 1),
    CONSTRAINT max_members_positive CHECK (max_members > 0)
);

-- Group members table - Group membership management
CREATE TABLE group_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role group_role DEFAULT 'member',
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(group_id, user_id)
);

-- Messages table - Temporary message storage with auto-expiration
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sender_id UUID NOT NULL REFERENCES users(id),
    sender_device_id UUID NOT NULL REFERENCES devices(id),
    group_id UUID REFERENCES groups(id), -- NULL for direct messages
    recipient_id UUID REFERENCES users(id), -- NULL for group messages
    
    -- Encrypted content and metadata
    encrypted_content TEXT NOT NULL, -- Post-quantum encrypted message content
    content_signature TEXT NOT NULL, -- Dilithium signature for authenticity
    encryption_metadata JSONB NOT NULL, -- Key exchange and encryption parameters
    
    -- Message lifecycle
    status message_status DEFAULT 'sending',
    expires_at TIMESTAMPTZ NOT NULL, -- Auto-destruction timestamp
    read_at TIMESTAMPTZ,
    destroyed_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_recipient CHECK (
        (group_id IS NOT NULL AND recipient_id IS NULL) OR 
        (group_id IS NULL AND recipient_id IS NOT NULL)
    ),
    CONSTRAINT expires_in_future CHECK (expires_at > created_at),
    CONSTRAINT content_not_empty CHECK (char_length(encrypted_content) > 0)
);

-- Message recipients table - Track delivery and read status for group messages
CREATE TABLE message_recipients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES users(id),
    status message_status DEFAULT 'sent',
    delivered_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ,
    
    UNIQUE(message_id, recipient_id)
);

-- Key exchange sessions - Temporary key exchange data
CREATE TABLE key_exchange_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    initiator_id UUID NOT NULL REFERENCES users(id),
    responder_id UUID NOT NULL REFERENCES users(id),
    initiator_device_id UUID NOT NULL REFERENCES devices(id),
    responder_device_id UUID REFERENCES devices(id),
    
    -- Key exchange data (Kyber KEM)
    public_key_share TEXT NOT NULL,
    encrypted_shared_secret TEXT,
    session_signature TEXT NOT NULL, -- Dilithium signature
    
    status VARCHAR(20) DEFAULT 'pending', -- pending, completed, expired
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '1 hour'),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT different_users CHECK (initiator_id != responder_id)
);

-- Invite codes table - Secure invitation system
CREATE TABLE invite_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(12) UNIQUE NOT NULL, -- Short, secure invite code
    created_by UUID NOT NULL REFERENCES users(id),
    group_id UUID REFERENCES groups(id), -- NULL for general invites
    max_uses INTEGER DEFAULT 1,
    current_uses INTEGER DEFAULT 0,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT max_uses_positive CHECK (max_uses > 0),
    CONSTRAINT current_uses_valid CHECK (current_uses >= 0 AND current_uses <= max_uses)
);

-- Audit log for security monitoring
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    device_id UUID REFERENCES devices(id),
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
