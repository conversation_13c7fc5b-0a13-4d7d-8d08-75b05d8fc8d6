-- Indexes and Triggers for Crystal Chat
-- Performance optimization and automated cleanup

-- Performance indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_last_seen ON users(last_seen);

CREATE INDEX idx_devices_user_id ON devices(user_id);
CREATE INDEX idx_devices_fingerprint ON devices(device_fingerprint);
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_devices_last_used ON devices(last_used);

CREATE INDEX idx_groups_created_by ON groups(created_by);
CREATE INDEX idx_groups_created_at ON groups(created_at);

CREATE INDEX idx_group_members_group_id ON group_members(group_id);
CREATE INDEX idx_group_members_user_id ON group_members(user_id);
CREATE INDEX idx_group_members_role ON group_members(role);

CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_recipient_id ON messages(recipient_id);
CREATE INDEX idx_messages_group_id ON messages(group_id);
CREATE INDEX idx_messages_status ON messages(status);
CREATE INDEX idx_messages_expires_at ON messages(expires_at);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_destroyed_at ON messages(destroyed_at);

CREATE INDEX idx_message_recipients_message_id ON message_recipients(message_id);
CREATE INDEX idx_message_recipients_recipient_id ON message_recipients(recipient_id);
CREATE INDEX idx_message_recipients_status ON message_recipients(status);

CREATE INDEX idx_key_exchange_initiator ON key_exchange_sessions(initiator_id);
CREATE INDEX idx_key_exchange_responder ON key_exchange_sessions(responder_id);
CREATE INDEX idx_key_exchange_status ON key_exchange_sessions(status);
CREATE INDEX idx_key_exchange_expires_at ON key_exchange_sessions(expires_at);

CREATE INDEX idx_invite_codes_code ON invite_codes(code);
CREATE INDEX idx_invite_codes_created_by ON invite_codes(created_by);
CREATE INDEX idx_invite_codes_group_id ON invite_codes(group_id);
CREATE INDEX idx_invite_codes_expires_at ON invite_codes(expires_at);

CREATE INDEX idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX idx_audit_log_action ON audit_log(action);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at columns
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_groups_updated_at 
    BEFORE UPDATE ON groups 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically destroy expired messages
CREATE OR REPLACE FUNCTION destroy_expired_messages()
RETURNS void AS $$
BEGIN
    -- Mark expired messages as destroyed
    UPDATE messages 
    SET status = 'destroyed', destroyed_at = NOW()
    WHERE expires_at <= NOW() 
    AND status NOT IN ('destroyed', 'expired');
    
    -- Clean up expired key exchange sessions
    DELETE FROM key_exchange_sessions 
    WHERE expires_at <= NOW();
    
    -- Clean up expired invite codes
    DELETE FROM invite_codes 
    WHERE expires_at <= NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to clean up destroyed messages (complete removal)
CREATE OR REPLACE FUNCTION cleanup_destroyed_messages()
RETURNS void AS $$
BEGIN
    -- Remove message recipients for destroyed messages
    DELETE FROM message_recipients 
    WHERE message_id IN (
        SELECT id FROM messages 
        WHERE status = 'destroyed' 
        AND destroyed_at < NOW() - INTERVAL '1 hour'
    );
    
    -- Remove destroyed messages after 1 hour
    DELETE FROM messages 
    WHERE status = 'destroyed' 
    AND destroyed_at < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql;

-- Function to audit user actions
CREATE OR REPLACE FUNCTION audit_user_action()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_log (
        user_id, 
        action, 
        resource_type, 
        resource_id, 
        metadata,
        created_at
    ) VALUES (
        COALESCE(NEW.user_id, OLD.user_id),
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        jsonb_build_object(
            'old', to_jsonb(OLD),
            'new', to_jsonb(NEW)
        ),
        NOW()
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Audit triggers for security monitoring
CREATE TRIGGER audit_users_changes
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_user_action();

CREATE TRIGGER audit_devices_changes
    AFTER INSERT OR UPDATE OR DELETE ON devices
    FOR EACH ROW EXECUTE FUNCTION audit_user_action();

CREATE TRIGGER audit_groups_changes
    AFTER INSERT OR UPDATE OR DELETE ON groups
    FOR EACH ROW EXECUTE FUNCTION audit_user_action();

CREATE TRIGGER audit_messages_changes
    AFTER INSERT OR UPDATE OR DELETE ON messages
    FOR EACH ROW EXECUTE FUNCTION audit_user_action();

-- Function to validate message expiration
CREATE OR REPLACE FUNCTION validate_message_expiration()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure messages expire within reasonable timeframe (max 24 hours)
    IF NEW.expires_at > NOW() + INTERVAL '24 hours' THEN
        RAISE EXCEPTION 'Message expiration cannot be more than 24 hours in the future';
    END IF;
    
    -- Ensure minimum expiration time (1 minute)
    IF NEW.expires_at < NOW() + INTERVAL '1 minute' THEN
        RAISE EXCEPTION 'Message expiration must be at least 1 minute in the future';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER validate_message_expiration_trigger
    BEFORE INSERT OR UPDATE ON messages
    FOR EACH ROW EXECUTE FUNCTION validate_message_expiration();

-- Function to automatically update message status
CREATE OR REPLACE FUNCTION update_message_status()
RETURNS TRIGGER AS $$
BEGIN
    -- When a message is read, update the read timestamp
    IF NEW.status = 'read' AND OLD.status != 'read' THEN
        NEW.read_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_message_status_trigger
    BEFORE UPDATE ON messages
    FOR EACH ROW EXECUTE FUNCTION update_message_status();
