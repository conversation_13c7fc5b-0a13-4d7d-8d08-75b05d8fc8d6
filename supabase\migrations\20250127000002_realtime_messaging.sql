-- Real-time Messaging Infrastructure Migration
-- Adds support for real-time messaging, typing indicators, presence, and message lifecycle

-- Add missing columns to messages table for lifecycle management
ALTER TABLE messages 
ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS auto_destroy_on_read BOOLEAN DEFAULT true;

-- Create message recipients table for group messaging and delivery tracking
CREATE TABLE IF NOT EXISTS message_recipients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  recipient_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  status message_status DEFAULT 'sent',
  delivered_at TIMESTAMPTZ,
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(message_id, recipient_id)
);

-- Create typing indicators table for real-time typing status
CREATE TABLE IF NOT EXISTS typing_indicators (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  conversation_id TEXT NOT NULL, -- Can be user_id for DM or group_id for groups
  is_typing BOOLEAN DEFAULT false,
  last_activity TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, conversation_id)
);

-- Create presence table for online/offline status
CREATE TABLE IF NOT EXISTS user_presence (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  status TEXT CHECK (status IN ('online', 'away', 'offline')) DEFAULT 'offline',
  last_seen TIMESTAMPTZ DEFAULT NOW(),
  device_id TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, device_id)
);

-- Create connection logs table for monitoring
CREATE TABLE IF NOT EXISTS connection_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL CHECK (event_type IN ('connect', 'disconnect', 'reconnect')),
  device_id TEXT,
  ip_address INET,
  user_agent TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_message_recipients_message_id ON message_recipients(message_id);
CREATE INDEX IF NOT EXISTS idx_message_recipients_recipient_id ON message_recipients(recipient_id);
CREATE INDEX IF NOT EXISTS idx_message_recipients_status ON message_recipients(status);

CREATE INDEX IF NOT EXISTS idx_typing_indicators_conversation ON typing_indicators(conversation_id);
CREATE INDEX IF NOT EXISTS idx_typing_indicators_user_id ON typing_indicators(user_id);
CREATE INDEX IF NOT EXISTS idx_typing_indicators_activity ON typing_indicators(last_activity);

CREATE INDEX IF NOT EXISTS idx_user_presence_user_id ON user_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_status ON user_presence(status);
CREATE INDEX IF NOT EXISTS idx_user_presence_last_seen ON user_presence(last_seen);

CREATE INDEX IF NOT EXISTS idx_connection_logs_user_id ON connection_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_connection_logs_created_at ON connection_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_messages_expires_at ON messages(expires_at);
CREATE INDEX IF NOT EXISTS idx_messages_delivered_at ON messages(delivered_at);

-- Add RLS policies for message recipients
ALTER TABLE message_recipients ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own message recipients" ON message_recipients
  FOR SELECT USING (
    recipient_id = auth.uid() OR 
    message_id IN (SELECT id FROM messages WHERE sender_id = auth.uid())
  );

CREATE POLICY "Users can insert message recipients for their messages" ON message_recipients
  FOR INSERT WITH CHECK (
    message_id IN (SELECT id FROM messages WHERE sender_id = auth.uid())
  );

CREATE POLICY "Users can update their own message recipient status" ON message_recipients
  FOR UPDATE USING (recipient_id = auth.uid());

-- Add RLS policies for typing indicators
ALTER TABLE typing_indicators ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own typing indicators" ON typing_indicators
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view typing indicators in their conversations" ON typing_indicators
  FOR SELECT USING (
    -- For DM conversations, check if user is part of the conversation
    conversation_id = auth.uid()::text OR
    -- For group conversations, check if user is a member (simplified for now)
    user_id = auth.uid()
  );

-- Add RLS policies for user presence
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own presence" ON user_presence
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view all user presence" ON user_presence
  FOR SELECT USING (true); -- Allow viewing all presence for now

-- Add RLS policies for connection logs
ALTER TABLE connection_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own connection logs" ON connection_logs
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can insert connection logs" ON connection_logs
  FOR INSERT WITH CHECK (true); -- Allow system to log connections

-- Create function to automatically clean up expired messages
CREATE OR REPLACE FUNCTION cleanup_expired_messages()
RETURNS void AS $$
BEGIN
  -- Mark expired messages as destroyed
  UPDATE messages 
  SET 
    status = 'destroyed',
    destroyed_at = NOW(),
    encrypted_content = '',
    content_signature = '',
    encryption_metadata = '{}'
  WHERE 
    expires_at <= NOW() 
    AND status != 'destroyed';
    
  -- Clean up old typing indicators (older than 5 minutes)
  DELETE FROM typing_indicators 
  WHERE last_activity < NOW() - INTERVAL '5 minutes';
  
  -- Update presence status for inactive users (offline after 5 minutes)
  UPDATE user_presence 
  SET 
    status = 'offline',
    updated_at = NOW()
  WHERE 
    last_seen < NOW() - INTERVAL '5 minutes' 
    AND status != 'offline';
    
  -- Clean up old connection logs (older than 30 days)
  DELETE FROM connection_logs 
  WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update message status automatically
CREATE OR REPLACE FUNCTION auto_update_message_status()
RETURNS trigger AS $$
BEGIN
  -- If message is being read, mark as read and potentially destroy
  IF NEW.status = 'read' AND OLD.status != 'read' THEN
    NEW.read_at = NOW();
    
    -- If auto-destroy on read is enabled, mark as destroyed
    IF NEW.auto_destroy_on_read = true THEN
      NEW.status = 'destroyed';
      NEW.destroyed_at = NOW();
      NEW.encrypted_content = '';
      NEW.content_signature = '';
      NEW.encryption_metadata = '{}';
    END IF;
  END IF;
  
  -- If message is being delivered, mark delivery time
  IF NEW.status = 'delivered' AND OLD.status != 'delivered' THEN
    NEW.delivered_at = NOW();
  END IF;
  
  -- Check if message has expired
  IF NEW.expires_at <= NOW() AND NEW.status != 'destroyed' THEN
    NEW.status = 'destroyed';
    NEW.destroyed_at = NOW();
    NEW.encrypted_content = '';
    NEW.content_signature = '';
    NEW.encryption_metadata = '{}';
  END IF;
  
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic message status updates
DROP TRIGGER IF EXISTS trigger_auto_update_message_status ON messages;
CREATE TRIGGER trigger_auto_update_message_status
  BEFORE UPDATE ON messages
  FOR EACH ROW
  EXECUTE FUNCTION auto_update_message_status();

-- Create function to update typing indicators
CREATE OR REPLACE FUNCTION update_typing_indicator(
  p_user_id UUID,
  p_conversation_id TEXT,
  p_is_typing BOOLEAN
)
RETURNS void AS $$
BEGIN
  INSERT INTO typing_indicators (user_id, conversation_id, is_typing, last_activity)
  VALUES (p_user_id, p_conversation_id, p_is_typing, NOW())
  ON CONFLICT (user_id, conversation_id)
  DO UPDATE SET
    is_typing = p_is_typing,
    last_activity = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update user presence
CREATE OR REPLACE FUNCTION update_user_presence(
  p_user_id UUID,
  p_status TEXT,
  p_device_id TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS void AS $$
BEGIN
  INSERT INTO user_presence (user_id, status, last_seen, device_id, metadata)
  VALUES (p_user_id, p_status, NOW(), p_device_id, p_metadata)
  ON CONFLICT (user_id, COALESCE(device_id, ''))
  DO UPDATE SET
    status = p_status,
    last_seen = NOW(),
    metadata = p_metadata,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to log connections
CREATE OR REPLACE FUNCTION log_connection(
  p_user_id UUID,
  p_event_type TEXT,
  p_device_id TEXT DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS void AS $$
BEGIN
  INSERT INTO connection_logs (user_id, event_type, device_id, ip_address, user_agent, metadata)
  VALUES (p_user_id, p_event_type, p_device_id, p_ip_address, p_user_agent, p_metadata);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable realtime for relevant tables
ALTER PUBLICATION supabase_realtime ADD TABLE messages;
ALTER PUBLICATION supabase_realtime ADD TABLE message_recipients;
ALTER PUBLICATION supabase_realtime ADD TABLE typing_indicators;
ALTER PUBLICATION supabase_realtime ADD TABLE user_presence;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
