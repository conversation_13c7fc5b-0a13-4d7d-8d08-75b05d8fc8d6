-- Invite System Migration
-- Creates invite-only signup system

-- <PERSON>reate invites table
CREATE TABLE IF NOT EXISTS invites (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT NOT NULL UNIQUE,
  created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  used_at TIMESTAMPTZ,
  used_by UUID REFERENCES users(id) ON DELETE SET NULL,
  max_uses INTEGER DEFAULT 1 CHECK (max_uses > 0),
  current_uses INTEGER DEFAULT 0 CHECK (current_uses >= 0),
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  
  CONSTRAINT valid_usage CHECK (current_uses <= max_uses)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_invites_code ON invites(code);
CREATE INDEX IF NOT EXISTS idx_invites_created_by ON invites(created_by);
CREATE INDEX IF NOT EXISTS idx_invites_active ON invites(is_active);
CREATE INDEX IF NOT EXISTS idx_invites_expires_at ON invites(expires_at);
CREATE INDEX IF NOT EXISTS idx_invites_used_by ON invites(used_by);

-- Add invite_code column to users table to track which invite was used
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS invite_code TEXT REFERENCES invites(code) ON DELETE SET NULL;

-- Create index for invite_code in users table
CREATE INDEX IF NOT EXISTS idx_users_invite_code ON users(invite_code);

-- Enable RLS for invites table
ALTER TABLE invites ENABLE ROW LEVEL SECURITY;

-- RLS Policies for invites table
CREATE POLICY "Users can view invites they created" ON invites
  FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can create invites" ON invites
  FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their own invites" ON invites
  FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Users can delete their own invites" ON invites
  FOR DELETE USING (created_by = auth.uid());

-- Allow public read access for invite validation (but only code and basic info)
CREATE POLICY "Public can validate invite codes" ON invites
  FOR SELECT USING (true);

-- Function to validate invite code
CREATE OR REPLACE FUNCTION validate_invite_code(invite_code TEXT)
RETURNS TABLE(
  is_valid BOOLEAN,
  invite_id UUID,
  expires_at TIMESTAMPTZ,
  current_uses INTEGER,
  max_uses INTEGER,
  error_message TEXT
) AS $$
DECLARE
  invite_record invites%ROWTYPE;
BEGIN
  -- Get the invite record
  SELECT * INTO invite_record
  FROM invites
  WHERE code = UPPER(invite_code)
    AND is_active = true;

  -- Check if invite exists
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, NULL::UUID, NULL::TIMESTAMPTZ, 0, 0, 'Invalid invite code';
    RETURN;
  END IF;

  -- Check if expired
  IF invite_record.expires_at <= NOW() THEN
    RETURN QUERY SELECT false, invite_record.id, invite_record.expires_at, 
                        invite_record.current_uses, invite_record.max_uses, 
                        'Invite code has expired';
    RETURN;
  END IF;

  -- Check if max uses reached
  IF invite_record.current_uses >= invite_record.max_uses THEN
    RETURN QUERY SELECT false, invite_record.id, invite_record.expires_at,
                        invite_record.current_uses, invite_record.max_uses,
                        'Invite code has been fully used';
    RETURN;
  END IF;

  -- Valid invite
  RETURN QUERY SELECT true, invite_record.id, invite_record.expires_at,
                      invite_record.current_uses, invite_record.max_uses,
                      NULL::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to use an invite code
CREATE OR REPLACE FUNCTION use_invite_code(
  invite_code TEXT,
  user_id UUID
)
RETURNS TABLE(
  success BOOLEAN,
  error_message TEXT
) AS $$
DECLARE
  invite_record invites%ROWTYPE;
  new_usage_count INTEGER;
BEGIN
  -- Get and lock the invite record
  SELECT * INTO invite_record
  FROM invites
  WHERE code = UPPER(invite_code)
    AND is_active = true
  FOR UPDATE;

  -- Check if invite exists
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 'Invalid invite code';
    RETURN;
  END IF;

  -- Check if expired
  IF invite_record.expires_at <= NOW() THEN
    RETURN QUERY SELECT false, 'Invite code has expired';
    RETURN;
  END IF;

  -- Check if max uses reached
  IF invite_record.current_uses >= invite_record.max_uses THEN
    RETURN QUERY SELECT false, 'Invite code has been fully used';
    RETURN;
  END IF;

  -- Increment usage count
  new_usage_count := invite_record.current_uses + 1;

  -- Update the invite
  UPDATE invites
  SET 
    current_uses = new_usage_count,
    used_at = NOW(),
    used_by = user_id,
    is_active = CASE 
      WHEN new_usage_count >= max_uses THEN false 
      ELSE true 
    END
  WHERE id = invite_record.id;

  -- Update user's invite_code
  UPDATE users
  SET invite_code = invite_code
  WHERE id = user_id;

  RETURN QUERY SELECT true, NULL::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired invites
CREATE OR REPLACE FUNCTION cleanup_expired_invites()
RETURNS INTEGER AS $$
DECLARE
  cleanup_count INTEGER;
BEGIN
  -- Deactivate expired invites
  UPDATE invites
  SET is_active = false
  WHERE expires_at <= NOW()
    AND is_active = true;

  GET DIAGNOSTICS cleanup_count = ROW_COUNT;
  
  RETURN cleanup_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get invite statistics
CREATE OR REPLACE FUNCTION get_invite_stats(user_id UUID)
RETURNS TABLE(
  total_created INTEGER,
  total_used INTEGER,
  active_invites INTEGER,
  expired_invites INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_created,
    COUNT(CASE WHEN current_uses > 0 THEN 1 END)::INTEGER as total_used,
    COUNT(CASE 
      WHEN is_active = true 
        AND expires_at > NOW() 
        AND current_uses < max_uses 
      THEN 1 
    END)::INTEGER as active_invites,
    COUNT(CASE 
      WHEN expires_at <= NOW() 
        OR current_uses >= max_uses 
      THEN 1 
    END)::INTEGER as expired_invites
  FROM invites
  WHERE created_by = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to automatically clean up expired invites
CREATE OR REPLACE FUNCTION trigger_cleanup_expired_invites()
RETURNS trigger AS $$
BEGIN
  -- Clean up expired invites when a new invite is created
  PERFORM cleanup_expired_invites();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic cleanup
DROP TRIGGER IF EXISTS trigger_invite_cleanup ON invites;
CREATE TRIGGER trigger_invite_cleanup
  AFTER INSERT ON invites
  FOR EACH STATEMENT
  EXECUTE FUNCTION trigger_cleanup_expired_invites();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON TABLE invites TO authenticated;
GRANT EXECUTE ON FUNCTION validate_invite_code(TEXT) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION use_invite_code(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_invites() TO authenticated;
GRANT EXECUTE ON FUNCTION get_invite_stats(UUID) TO authenticated;

-- Admin invite will be created in the admin seed migration (20250127000005_admin_seed.sql)
-- This ensures the admin user exists before creating the invite
