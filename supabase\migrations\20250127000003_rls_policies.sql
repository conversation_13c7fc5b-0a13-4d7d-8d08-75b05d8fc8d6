-- Row Level Security Policies for Crystal Chat
-- Ensures users can only access their authorized data

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_recipients ENABLE ROW LEVEL SECURITY;
ALTER TABLE key_exchange_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE invite_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user ID from JWT
CREATE OR REPLACE FUNCTION auth.user_id() RETURNS UUID AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'sub',
    (auth.jwt() ->> 'user_id')
  )::UUID
$$ LANGUAGE SQL STABLE;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (id = auth.user_id());

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (id = auth.user_id());

CREATE POLICY "Users can view other users in shared groups" ON users
    FOR SELECT USING (
        id IN (
            SELECT DISTINCT gm2.user_id 
            FROM group_members gm1 
            JOIN group_members gm2 ON gm1.group_id = gm2.group_id 
            WHERE gm1.user_id = auth.user_id()
        )
    );

-- Devices table policies
CREATE POLICY "Users can view their own devices" ON devices
    FOR SELECT USING (user_id = auth.user_id());

CREATE POLICY "Users can insert their own devices" ON devices
    FOR INSERT WITH CHECK (user_id = auth.user_id());

CREATE POLICY "Users can update their own devices" ON devices
    FOR UPDATE USING (user_id = auth.user_id());

CREATE POLICY "Users can delete their own devices" ON devices
    FOR DELETE USING (user_id = auth.user_id());

-- Groups table policies
CREATE POLICY "Users can view groups they are members of" ON groups
    FOR SELECT USING (
        id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can create groups" ON groups
    FOR INSERT WITH CHECK (created_by = auth.user_id());

CREATE POLICY "Group owners and admins can update groups" ON groups
    FOR UPDATE USING (
        id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id() 
            AND role IN ('owner', 'admin')
        )
    );

CREATE POLICY "Group owners can delete groups" ON groups
    FOR DELETE USING (
        id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id() 
            AND role = 'owner'
        )
    );

-- Group members table policies
CREATE POLICY "Users can view group members of their groups" ON group_members
    FOR SELECT USING (
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id()
        )
    );

CREATE POLICY "Group owners and admins can add members" ON group_members
    FOR INSERT WITH CHECK (
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id() 
            AND role IN ('owner', 'admin')
        )
    );

CREATE POLICY "Group owners and admins can update member roles" ON group_members
    FOR UPDATE USING (
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id() 
            AND role IN ('owner', 'admin')
        )
    );

CREATE POLICY "Users can leave groups" ON group_members
    FOR DELETE USING (user_id = auth.user_id());

CREATE POLICY "Group owners and admins can remove members" ON group_members
    FOR DELETE USING (
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id() 
            AND role IN ('owner', 'admin')
        )
    );

-- Messages table policies
CREATE POLICY "Users can view messages they sent" ON messages
    FOR SELECT USING (sender_id = auth.user_id());

CREATE POLICY "Users can view direct messages sent to them" ON messages
    FOR SELECT USING (
        recipient_id = auth.user_id() 
        AND group_id IS NULL
    );

CREATE POLICY "Users can view group messages in their groups" ON messages
    FOR SELECT USING (
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can send messages" ON messages
    FOR INSERT WITH CHECK (
        sender_id = auth.user_id() 
        AND (
            -- Direct message to valid recipient
            (group_id IS NULL AND recipient_id IS NOT NULL) 
            OR 
            -- Group message to group they're a member of
            (group_id IS NOT NULL AND recipient_id IS NULL AND group_id IN (
                SELECT group_id FROM group_members 
                WHERE user_id = auth.user_id()
            ))
        )
    );

CREATE POLICY "Users can update their own message status" ON messages
    FOR UPDATE USING (sender_id = auth.user_id());

-- Message recipients table policies
CREATE POLICY "Recipients can view their message delivery status" ON message_recipients
    FOR SELECT USING (recipient_id = auth.user_id());

CREATE POLICY "Recipients can update their message status" ON message_recipients
    FOR UPDATE USING (recipient_id = auth.user_id());

CREATE POLICY "System can insert message recipients" ON message_recipients
    FOR INSERT WITH CHECK (true); -- Controlled by application logic

-- Key exchange sessions table policies
CREATE POLICY "Users can view their key exchange sessions" ON key_exchange_sessions
    FOR SELECT USING (
        initiator_id = auth.user_id() 
        OR responder_id = auth.user_id()
    );

CREATE POLICY "Users can create key exchange sessions" ON key_exchange_sessions
    FOR INSERT WITH CHECK (initiator_id = auth.user_id());

CREATE POLICY "Users can update their key exchange sessions" ON key_exchange_sessions
    FOR UPDATE USING (
        initiator_id = auth.user_id() 
        OR responder_id = auth.user_id()
    );

-- Invite codes table policies
CREATE POLICY "Users can view invite codes they created" ON invite_codes
    FOR SELECT USING (created_by = auth.user_id());

CREATE POLICY "Users can view invite codes for groups they're in" ON invite_codes
    FOR SELECT USING (
        group_id IN (
            SELECT group_id FROM group_members 
            WHERE user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can create invite codes" ON invite_codes
    FOR INSERT WITH CHECK (
        created_by = auth.user_id() 
        AND (
            group_id IS NULL 
            OR group_id IN (
                SELECT group_id FROM group_members 
                WHERE user_id = auth.user_id() 
                AND role IN ('owner', 'admin')
            )
        )
    );

CREATE POLICY "Users can update their invite codes" ON invite_codes
    FOR UPDATE USING (created_by = auth.user_id());

CREATE POLICY "Users can delete their invite codes" ON invite_codes
    FOR DELETE USING (created_by = auth.user_id());

-- Audit log policies (read-only for users)
CREATE POLICY "Users can view their own audit logs" ON audit_log
    FOR SELECT USING (user_id = auth.user_id());
