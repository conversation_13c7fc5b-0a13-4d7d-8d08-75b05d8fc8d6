-- Database Functions for Crystal Chat
-- Application logic and utility functions

-- Function to create a new user with secret word authentication
CREATE OR REPLACE FUNCTION create_user_with_secret(
    p_username VARCHAR(50),
    p_secret_word TEXT,
    p_display_name VARCHAR(100) DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    user_id UUID;
    secret_hash TEXT;
BEGIN
    -- Hash the secret word using bcrypt
    secret_hash := crypt(p_secret_word, gen_salt('bf', 12));
    
    -- Insert the new user
    INSERT INTO users (username, secret_word_hash, display_name)
    VALUES (p_username, secret_hash, COALESCE(p_display_name, p_username))
    RETURNING id INTO user_id;
    
    RETURN user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to authenticate user with secret word
CREATE OR REPLACE FUNCTION authenticate_user(
    p_username VARCHA<PERSON>(50),
    p_secret_word TEXT
)
RETURNS TABLE(user_id UUID, success BOOLEAN) AS $$
DECLARE
    stored_hash TEXT;
    user_record RECORD;
BEGIN
    -- Get user record
    SELECT id, secret_word_hash INTO user_record
    FROM users 
    WHERE username = p_username AND status = 'active';
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT NULL::UUID, FALSE;
        RETURN;
    END IF;
    
    -- Verify secret word
    IF crypt(p_secret_word, user_record.secret_word_hash) = user_record.secret_word_hash THEN
        -- Update last seen
        UPDATE users SET last_seen = NOW() WHERE id = user_record.id;
        RETURN QUERY SELECT user_record.id, TRUE;
    ELSE
        RETURN QUERY SELECT NULL::UUID, FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to register a new device
CREATE OR REPLACE FUNCTION register_device(
    p_user_id UUID,
    p_device_name VARCHAR(100),
    p_device_fingerprint TEXT,
    p_public_key_dilithium TEXT,
    p_public_key_kyber TEXT
)
RETURNS UUID AS $$
DECLARE
    device_id UUID;
BEGIN
    INSERT INTO devices (
        user_id, 
        device_name, 
        device_fingerprint, 
        public_key_dilithium, 
        public_key_kyber
    )
    VALUES (
        p_user_id, 
        p_device_name, 
        p_device_fingerprint, 
        p_public_key_dilithium, 
        p_public_key_kyber
    )
    RETURNING id INTO device_id;
    
    RETURN device_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a new group
CREATE OR REPLACE FUNCTION create_group(
    p_name VARCHAR(100),
    p_description TEXT,
    p_created_by UUID,
    p_group_key_kyber TEXT,
    p_max_members INTEGER DEFAULT 50
)
RETURNS UUID AS $$
DECLARE
    group_id UUID;
BEGIN
    -- Create the group
    INSERT INTO groups (name, description, created_by, group_key_kyber, max_members)
    VALUES (p_name, p_description, p_created_by, p_group_key_kyber, p_max_members)
    RETURNING id INTO group_id;
    
    -- Add creator as owner
    INSERT INTO group_members (group_id, user_id, role)
    VALUES (group_id, p_created_by, 'owner');
    
    RETURN group_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send a message
CREATE OR REPLACE FUNCTION send_message(
    p_sender_id UUID,
    p_sender_device_id UUID,
    p_group_id UUID DEFAULT NULL,
    p_recipient_id UUID DEFAULT NULL,
    p_encrypted_content TEXT,
    p_content_signature TEXT,
    p_encryption_metadata JSONB,
    p_expires_in_minutes INTEGER DEFAULT 60
)
RETURNS UUID AS $$
DECLARE
    message_id UUID;
    expires_at TIMESTAMPTZ;
    recipient_user_id UUID;
BEGIN
    -- Calculate expiration time
    expires_at := NOW() + (p_expires_in_minutes || ' minutes')::INTERVAL;
    
    -- Validate message type
    IF (p_group_id IS NULL AND p_recipient_id IS NULL) OR 
       (p_group_id IS NOT NULL AND p_recipient_id IS NOT NULL) THEN
        RAISE EXCEPTION 'Message must be either direct (recipient_id) or group (group_id), not both or neither';
    END IF;
    
    -- Insert the message
    INSERT INTO messages (
        sender_id,
        sender_device_id,
        group_id,
        recipient_id,
        encrypted_content,
        content_signature,
        encryption_metadata,
        expires_at
    )
    VALUES (
        p_sender_id,
        p_sender_device_id,
        p_group_id,
        p_recipient_id,
        p_encrypted_content,
        p_content_signature,
        p_encryption_metadata,
        expires_at
    )
    RETURNING id INTO message_id;
    
    -- For group messages, create recipient records
    IF p_group_id IS NOT NULL THEN
        INSERT INTO message_recipients (message_id, recipient_id)
        SELECT message_id, user_id
        FROM group_members
        WHERE group_id = p_group_id AND user_id != p_sender_id;
    ELSE
        -- For direct messages, create single recipient record
        INSERT INTO message_recipients (message_id, recipient_id)
        VALUES (message_id, p_recipient_id);
    END IF;
    
    RETURN message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark message as read
CREATE OR REPLACE FUNCTION mark_message_read(
    p_message_id UUID,
    p_user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    message_record RECORD;
BEGIN
    -- Get message details
    SELECT * INTO message_record FROM messages WHERE id = p_message_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check if user is authorized to read this message
    IF message_record.recipient_id = p_user_id OR 
       message_record.group_id IN (
           SELECT group_id FROM group_members WHERE user_id = p_user_id
       ) THEN
        
        -- Update message recipient status
        UPDATE message_recipients 
        SET status = 'read', read_at = NOW()
        WHERE message_id = p_message_id AND recipient_id = p_user_id;
        
        -- For direct messages, update the main message status
        IF message_record.group_id IS NULL THEN
            UPDATE messages 
            SET status = 'read', read_at = NOW()
            WHERE id = p_message_id;
        END IF;
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate invite code
CREATE OR REPLACE FUNCTION generate_invite_code(
    p_created_by UUID,
    p_group_id UUID DEFAULT NULL,
    p_max_uses INTEGER DEFAULT 1,
    p_expires_in_hours INTEGER DEFAULT 24
)
RETURNS VARCHAR(12) AS $$
DECLARE
    invite_code VARCHAR(12);
    expires_at TIMESTAMPTZ;
BEGIN
    -- Generate random 12-character code
    invite_code := upper(substring(md5(random()::text) from 1 for 12));
    expires_at := NOW() + (p_expires_in_hours || ' hours')::INTERVAL;
    
    -- Insert invite code
    INSERT INTO invite_codes (
        code, 
        created_by, 
        group_id, 
        max_uses, 
        expires_at
    )
    VALUES (
        invite_code, 
        p_created_by, 
        p_group_id, 
        p_max_uses, 
        expires_at
    );
    
    RETURN invite_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to use invite code
CREATE OR REPLACE FUNCTION use_invite_code(
    p_code VARCHAR(12),
    p_user_id UUID
)
RETURNS JSONB AS $$
DECLARE
    invite_record RECORD;
    result JSONB;
BEGIN
    -- Get invite code details
    SELECT * INTO invite_record 
    FROM invite_codes 
    WHERE code = p_code 
    AND expires_at > NOW() 
    AND current_uses < max_uses;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'Invalid or expired invite code');
    END IF;
    
    -- If it's a group invite, add user to group
    IF invite_record.group_id IS NOT NULL THEN
        -- Check if user is already a member
        IF EXISTS (
            SELECT 1 FROM group_members 
            WHERE group_id = invite_record.group_id AND user_id = p_user_id
        ) THEN
            RETURN jsonb_build_object('success', false, 'error', 'Already a member of this group');
        END IF;
        
        -- Add user to group
        INSERT INTO group_members (group_id, user_id, role)
        VALUES (invite_record.group_id, p_user_id, 'member');
        
        result := jsonb_build_object(
            'success', true, 
            'type', 'group',
            'group_id', invite_record.group_id
        );
    ELSE
        result := jsonb_build_object(
            'success', true, 
            'type', 'general'
        );
    END IF;
    
    -- Increment usage count
    UPDATE invite_codes 
    SET current_uses = current_uses + 1 
    WHERE id = invite_record.id;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
