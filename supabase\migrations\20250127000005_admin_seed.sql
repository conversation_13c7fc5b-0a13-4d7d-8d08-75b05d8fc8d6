-- Admin Account Seed Data
-- Creates initial admin account with specified credentials
-- Email: <EMAIL>, Username: QSC-buyinza

-- Create the admin user
DO $$
DECLARE
    admin_user_id UUID;
    admin_invite_code TEXT := 'ADMIN001';
BEGIN
    -- Create admin user with specified credentials
    SELECT create_user_with_secret(
        'QSC-buyinza',                    -- username
        'AdminSecure123!',                -- temporary secret word (should be changed on first login)
        'QSC Admin'                       -- display name
    ) INTO admin_user_id;

    -- Log the admin user creation
    RAISE NOTICE 'Admin user created with ID: %', admin_user_id;

    -- Create an admin invite code that the admin can use to invite others
    INSERT INTO invites (
        code,
        created_by,
        expires_at,
        max_uses,
        current_uses,
        is_active,
        metadata
    ) VALUES (
        admin_invite_code,
        admin_user_id,
        NOW() + INTERVAL '30 days',       -- Expires in 30 days
        10,                               -- Can be used 10 times
        0,                                -- Not used yet
        true,                             -- Active
        jsonb_build_object(
            'type', 'admin_created',
            'description', 'Admin invite for initial user onboarding',
            'created_for', '<EMAIL>'
        )
    ) ON CONFLICT (code) DO UPDATE SET
        created_by = EXCLUDED.created_by,
        expires_at = EXCLUDED.expires_at,
        max_uses = EXCLUDED.max_uses,
        is_active = EXCLUDED.is_active,
        metadata = EXCLUDED.metadata;

    -- Log the invite creation
    RAISE NOTICE 'Admin invite code created: %', admin_invite_code;

EXCEPTION
    WHEN unique_violation THEN
        -- If username already exists, just update the invite
        SELECT id INTO admin_user_id FROM users WHERE username = 'QSC-buyinza';
        
        INSERT INTO invites (
            code,
            created_by,
            expires_at,
            max_uses,
            current_uses,
            is_active,
            metadata
        ) VALUES (
            admin_invite_code,
            admin_user_id,
            NOW() + INTERVAL '30 days',
            10,
            0,
            true,
            jsonb_build_object(
                'type', 'admin_created',
                'description', 'Admin invite for initial user onboarding',
                'created_for', '<EMAIL>'
            )
        ) ON CONFLICT (code) DO UPDATE SET
            expires_at = EXCLUDED.expires_at,
            max_uses = EXCLUDED.max_uses,
            is_active = EXCLUDED.is_active;
            
        RAISE NOTICE 'Admin user already exists, invite code updated: %', admin_invite_code;
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to create admin user: %', SQLERRM;
END $$;

-- Grant admin user any additional permissions if needed
-- (Currently using the same permissions as regular users)

-- Display setup information
DO $$
BEGIN
    RAISE NOTICE '=== ADMIN ACCOUNT SETUP COMPLETE ===';
    RAISE NOTICE 'Username: QSC-buyinza';
    RAISE NOTICE 'Email: <EMAIL>';
    RAISE NOTICE 'Temporary Secret Word: AdminSecure123!';
    RAISE NOTICE 'Admin Invite Code: ADMIN001';
    RAISE NOTICE 'IMPORTANT: Change the secret word on first login!';
    RAISE NOTICE '==========================================';
END $$;
