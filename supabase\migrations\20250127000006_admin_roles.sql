-- Admin Roles and Permissions System
-- Adds admin role functionality to the Crystal Chat system

-- Add admin role column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'admin', 'super_admin'));

-- Create index for role column
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- Update the existing admin user to have admin role
UPDATE users SET role = 'admin' WHERE username = 'QSC-buyinza';

-- Create admin functions for user management
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id AND role IN ('admin', 'super_admin')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all users (admin only)
CREATE OR REPLACE FUNCTION get_all_users()
RETURNS TABLE(
  id UUID,
  username VARCHAR(50),
  display_name VA<PERSON>HA<PERSON>(100),
  status user_status,
  role VARCHAR(20),
  last_seen TIMESTAMPTZ,
  created_at TIMESTAMPTZ,
  invite_code TEXT
) AS $$
BEGIN
  -- Check if current user is admin
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied: Admin privileges required';
  END IF;

  RETURN QUERY
  SELECT 
    u.id,
    u.username,
    u.display_name,
    u.status,
    u.role,
    u.last_seen,
    u.created_at,
    u.invite_code
  FROM users u
  ORDER BY u.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pending invite requests (admin only)
CREATE OR REPLACE FUNCTION get_pending_invites()
RETURNS TABLE(
  id UUID,
  code TEXT,
  created_by UUID,
  creator_username VARCHAR(50),
  created_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  max_uses INTEGER,
  current_uses INTEGER,
  metadata JSONB
) AS $$
BEGIN
  -- Check if current user is admin
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied: Admin privileges required';
  END IF;

  RETURN QUERY
  SELECT 
    i.id,
    i.code,
    i.created_by,
    u.username as creator_username,
    i.created_at,
    i.expires_at,
    i.max_uses,
    i.current_uses,
    i.metadata
  FROM invites i
  JOIN users u ON i.created_by = u.id
  WHERE i.is_active = true
  ORDER BY i.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to approve/reject invite requests (admin only)
CREATE OR REPLACE FUNCTION manage_invite_request(
  invite_id UUID,
  action TEXT, -- 'approve' or 'reject'
  admin_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  invite_record RECORD;
BEGIN
  -- Check if current user is admin
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied: Admin privileges required';
  END IF;

  -- Validate action
  IF action NOT IN ('approve', 'reject') THEN
    RAISE EXCEPTION 'Invalid action. Must be "approve" or "reject"';
  END IF;

  -- Get invite record
  SELECT * INTO invite_record FROM invites WHERE id = invite_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invite not found';
  END IF;

  IF action = 'approve' THEN
    -- Activate the invite
    UPDATE invites 
    SET 
      is_active = true,
      metadata = metadata || jsonb_build_object(
        'approved_by', auth.uid(),
        'approved_at', NOW(),
        'admin_notes', admin_notes
      )
    WHERE id = invite_id;
  ELSE
    -- Reject the invite (deactivate)
    UPDATE invites 
    SET 
      is_active = false,
      metadata = metadata || jsonb_build_object(
        'rejected_by', auth.uid(),
        'rejected_at', NOW(),
        'admin_notes', admin_notes
      )
    WHERE id = invite_id;
  END IF;

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get system statistics (admin only)
CREATE OR REPLACE FUNCTION get_system_stats()
RETURNS TABLE(
  total_users INTEGER,
  active_users INTEGER,
  total_messages INTEGER,
  messages_today INTEGER,
  total_invites INTEGER,
  pending_invites INTEGER,
  active_devices INTEGER
) AS $$
BEGIN
  -- Check if current user is admin
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied: Admin privileges required';
  END IF;

  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::INTEGER FROM users) as total_users,
    (SELECT COUNT(*)::INTEGER FROM users WHERE status = 'active') as active_users,
    (SELECT COUNT(*)::INTEGER FROM messages) as total_messages,
    (SELECT COUNT(*)::INTEGER FROM messages WHERE created_at >= CURRENT_DATE) as messages_today,
    (SELECT COUNT(*)::INTEGER FROM invites) as total_invites,
    (SELECT COUNT(*)::INTEGER FROM invites WHERE is_active = true AND expires_at > NOW()) as pending_invites,
    (SELECT COUNT(*)::INTEGER FROM devices WHERE status = 'active') as active_devices;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user status (admin only)
CREATE OR REPLACE FUNCTION admin_update_user_status(
  target_user_id UUID,
  new_status user_status,
  admin_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if current user is admin
  IF NOT is_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied: Admin privileges required';
  END IF;

  -- Prevent admins from suspending themselves
  IF target_user_id = auth.uid() AND new_status = 'suspended' THEN
    RAISE EXCEPTION 'Cannot suspend your own account';
  END IF;

  -- Update user status
  UPDATE users 
  SET 
    status = new_status,
    updated_at = NOW()
  WHERE id = target_user_id;

  -- Log the action in audit log
  INSERT INTO audit_log (
    user_id,
    action,
    resource_type,
    resource_id,
    metadata
  ) VALUES (
    auth.uid(),
    'admin_user_status_change',
    'users',
    target_user_id,
    jsonb_build_object(
      'new_status', new_status,
      'admin_notes', admin_notes,
      'target_user', target_user_id
    )
  );

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions to authenticated users for admin functions
GRANT EXECUTE ON FUNCTION is_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_users() TO authenticated;
GRANT EXECUTE ON FUNCTION get_pending_invites() TO authenticated;
GRANT EXECUTE ON FUNCTION manage_invite_request(UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_system_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION admin_update_user_status(UUID, user_status, TEXT) TO authenticated;
