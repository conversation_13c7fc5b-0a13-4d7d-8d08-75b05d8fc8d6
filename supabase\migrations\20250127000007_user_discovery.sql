-- User Discovery Functions for Anonymous Username Validation
-- These functions allow validating usernames without revealing user information

-- Function to validate username for messaging without revealing user details
CREATE OR REPLACE FUNCTION validate_username_for_messaging(
    p_username TEXT,
    p_check_messageability BOOLEAN DEFAULT TRUE
)
RETURNS TABLE(exists BOOLEAN, can_receive_messages BOOLEAN) AS $$
DECLARE
    user_record RECORD;
BEGIN
    -- Check if username exists and get basic status
    SELECT id, status, messaging_enabled INTO user_record
    FROM users 
    WHERE username = p_username;
    
    -- If user doesn't exist
    IF NOT FOUND THEN
        RETURN QUERY SELECT FALSE, FALSE;
        RETURN;
    END IF;
    
    -- User exists, now check if they can receive messages
    IF p_check_messageability THEN
        -- User can receive messages if:
        -- 1. Account is active
        -- 2. Messaging is enabled (if column exists)
        -- 3. Not suspended or banned
        RETURN QUERY SELECT 
            TRUE,
            (user_record.status = 'active' AND 
             COALESCE(user_record.messaging_enabled, TRUE));
    ELSE
        -- Just return existence
        RETURN QUERY SELECT TRUE, TRUE;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user can send message to specific username
CREATE OR REPLACE FUNCTION can_send_message_to_user(
    p_recipient_username TEXT
)
RETURNS TABLE(can_send BOOLEAN, reason TEXT) AS $$
DECLARE
    current_user_id UUID;
    recipient_record RECORD;
    is_blocked BOOLEAN := FALSE;
BEGIN
    -- Get current user ID from auth context
    current_user_id := auth.uid();
    
    -- If no authenticated user
    IF current_user_id IS NULL THEN
        RETURN QUERY SELECT FALSE, 'Authentication required';
        RETURN;
    END IF;
    
    -- Get recipient information
    SELECT id, status, messaging_enabled INTO recipient_record
    FROM users 
    WHERE username = p_recipient_username;
    
    -- If recipient doesn't exist
    IF NOT FOUND THEN
        RETURN QUERY SELECT FALSE, 'Recipient not found';
        RETURN;
    END IF;
    
    -- Check if recipient account is active
    IF recipient_record.status != 'active' THEN
        RETURN QUERY SELECT FALSE, 'Recipient account unavailable';
        RETURN;
    END IF;
    
    -- Check if recipient has messaging enabled
    IF COALESCE(recipient_record.messaging_enabled, TRUE) = FALSE THEN
        RETURN QUERY SELECT FALSE, 'Messaging not available';
        RETURN;
    END IF;
    
    -- Check if current user is blocked by recipient
    -- (This would require a blocks table if implemented)
    -- SELECT EXISTS(
    --     SELECT 1 FROM user_blocks 
    --     WHERE blocker_id = recipient_record.id 
    --     AND blocked_id = current_user_id
    -- ) INTO is_blocked;
    
    -- IF is_blocked THEN
    --     RETURN QUERY SELECT FALSE, 'Unable to send message';
    --     RETURN;
    -- END IF;
    
    -- Check if current user can send messages (not suspended, etc.)
    IF NOT EXISTS(
        SELECT 1 FROM users 
        WHERE id = current_user_id 
        AND status = 'active'
    ) THEN
        RETURN QUERY SELECT FALSE, 'Account restrictions apply';
        RETURN;
    END IF;
    
    -- All checks passed
    RETURN QUERY SELECT TRUE, NULL::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user ID by username (for internal use only)
-- This is used by messaging functions but doesn't expose user information
CREATE OR REPLACE FUNCTION get_user_id_by_username(
    p_username TEXT
)
RETURNS UUID AS $$
DECLARE
    user_id UUID;
BEGIN
    SELECT id INTO user_id
    FROM users 
    WHERE username = p_username 
    AND status = 'active';
    
    RETURN user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add messaging_enabled column to users table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'messaging_enabled'
    ) THEN
        ALTER TABLE users ADD COLUMN messaging_enabled BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Create index for username lookups if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'users' 
        AND indexname = 'idx_users_username_status'
    ) THEN
        CREATE INDEX idx_users_username_status ON users(username, status);
    END IF;
END $$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION validate_username_for_messaging(TEXT, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION can_send_message_to_user(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_id_by_username(TEXT) TO authenticated;

-- Add RLS policy for messaging_enabled column
DO $$
BEGIN
    -- Enable RLS on users table if not already enabled
    IF NOT EXISTS (
        SELECT 1 FROM pg_tables 
        WHERE tablename = 'users' 
        AND rowsecurity = true
    ) THEN
        ALTER TABLE users ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Create policy for users to update their own messaging preferences
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'users' 
        AND policyname = 'Users can update own messaging preferences'
    ) THEN
        CREATE POLICY "Users can update own messaging preferences" ON users
            FOR UPDATE USING (auth.uid() = id)
            WITH CHECK (auth.uid() = id);
    END IF;
END $$;
